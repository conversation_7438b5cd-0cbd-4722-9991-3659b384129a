#pragma warning( disable : 4482 )
#include "ActorBoss.h"
#include "CityConfig.h"
#include "PlayerControl.h"
//#include "GameEvent.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "SandboxGameDef.h"
#include "GameCamera.h"
#include "IClientGameManagerInterface.h"
#include "LivingAttrib.h"
#include "PlayerAttrib.h"
#include "MobAttrib.h"
#include "ScriptComponent.h"
#include "ClientActorFuncWrapper.h"
#include "SoundComponent.h"
#include "DropItemComponent.h"
#include "FireBurnComponent.h"
#include "EffectComponent.h"
#include "ActionAttrStateComponent.h"
#include "TriggerComponent.h"
#include "ClientInfoProxy.h"
#include "WeaponSkinMgr.h"
#include "RiddenComponent.h"
#include "ActorGeniusMgr.h"
#include "IMiniDeveloperProxy.h"
#include "TemperatureManager.h"
#include "TemperatureComponent.h"
#include "SandBoxManager.h"
#include "EffectManager.h"
#include "ActorHorse.h"
#include "AttackJsonManager.h"
#include "ClientActorProjectile.h"
#include "special_blockid.h"
#include "EntryGridDataComponent.h"
#include "GunGridDataComponent.h"
#include "GridContainer.h"

using namespace Rainbow;
using namespace MNSandbox;
//IMPLEMENT_COMPONENTCLASS(LivingAttrib)
//----------------------------------------------------------------------------------------------------------------------------------

const int gBuffTickMax = 199980; //9999*20

// 身体部位对应装备类型
std::map<ATTACK_BODY_TYPE, std::map<EQUIP_SLOT_TYPE,bool> > gBodyType2EquipSlotType = {
	{ATTACK_BODY_HEAD, { {EQUIP_HEAD,true}, {EQUIP_HEAD_LINING, true} }},
	{ATTACK_BODY_UPBODY, {{EQUIP_BREAST,true}, {EQUIP_BREAST_LINING,true} }},
	{ATTACK_BODY_DOWNBODY, {{EQUIP_LEGGING,true}, {EQUIP_LEGGING_LINING,true}, {EQUIP_SHOE,true}, {EQUIP_SHOE_LINING,true} }},
	{ATTACK_BODY_MAX, {{EQUIP_HEAD,true}, {EQUIP_BREAST,true},
		{EQUIP_LEGGING,true}, {EQUIP_SHOE,true}, {EQUIP_HEAD_LINING,true},
		{EQUIP_BREAST_LINING,true}, {EQUIP_LEGGING_LINING,true}, {EQUIP_SHOE_LINING,true}, {EQUIP_PIFENG,true}, {EQUIP_GROUP,true}}}
};

LivingAttrib::LivingAttrib() : ActorAttrib(), m_iStatusIdx(1), m_hasBadBuff(false), m_AttrShapeShiftDef(NULL)
{
	m_Buffs.clear(); 
	m_StatusAddAttInfo.clear(); 
	m_Oxygen = START_OXYGEN;
	for (int i = 0; i < 2; i++) {
		m_fBaseAttack[i] = -1.0f;
		m_fBaseArmor[i] = -1.0f;
	}
	memset(m_fBodyTypeArmor, 0, sizeof(m_fBodyTypeArmor));

	m_iWaterPressure = 0;
	m_SelfTemperature = 0.f;
	m_PosTemperature = 0.f;
	m_TemperatureDefend = 0.f;
	m_Temperature = 0.f;
	m_TemperatureBuffDefend = 0.f;
	m_TemperatureEquipDefend = 0.f;
	m_TemperatureEnchantDefend = 0.f;
	m_TickTemperatureChangeVal = 0.0015f;
	m_BaseTemperatureChangeVal = 0.015f;
	m_TemperatureBuffEffect = 0.0f;
	m_TickTemperatureChangeRate = 1.0f;
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	if (lua_const)
	{
		m_TickTemperatureChangeVal = lua_const->m_ConstTickTempChangeVal;
		m_BaseTemperatureChangeVal = lua_const->m_ConstBaseTempChangeVal;
		m_TickTemperatureChangeRate = lua_const->m_ConstTickTempChangeRate;
	}
	m_AreaSourceIndex = 0;
	m_CreateBlockEnable = false;
	m_LiquidBlockEnable = false;
	m_AreaSourceWait = false;
	m_FinalPosTemperature = 0.f;
	m_lastAttackBuffLv = 0;
	m_lastAttackBuffId = 0;
	m_Tick = 0;
	m_ToughnessTotal = 0;
	m_ToughnessBase = 0;
	m_ToughnessEquip = 0;
	m_ToughnessTotalMax = 0;
	m_attackedNumMap.clear();
	m_FirstSearch = false;
	CreateEvent2();
}


LivingAttrib::~LivingAttrib()
{
	tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "LivingAttrib");
	m_Buffs.clear();
	m_StatusAddAttInfo.clear();
	m_attackedNumMap.clear();
	m_tempBuffsMap.clear();
	m_EquipType2Pos.clear();
	m_EquipPos2Type.clear();
	DestroyEvent2();
}

void LivingAttrib::CreateEvent2()
{
	typedef ListenerFunctionRef<bool&> ListenerBlockClipTrap;
	m_listenerBlockClipTrap = SANDBOX_NEW(ListenerBlockClipTrap, [&](bool& result) -> void {
		auto actor = this->getOwnerActor();
		ActorLiving* living = dynamic_cast<ActorLiving*>(actor);
		if (living && this->hasBuff(LOCK_MOB_BUFF))
		{
			result = true;
		}
		});
	Event2().Subscribe("BlockClipTrap_Collide", m_listenerBlockClipTrap);

	typedef ListenerFunctionRef<int, int, int, int, long long> Listener1;
	m_listenerLiving1 = SANDBOX_NEW(Listener1, [&](int buffid, int bufflv, int customticks, int buffInstanceId, long long objid) -> void {
		
		this->addBuff(buffid, bufflv, customticks, buffInstanceId, objid);
		});
	Event2().Subscribe("LivingAttrib_addBuff", m_listenerLiving1);

	typedef ListenerFunctionRef<int, bool, int> Listener2;
	m_listenerLiving2 = SANDBOX_NEW(Listener2, [&](int buffid, bool bRemoveSingleOne /* = true */, int buffInstanceId /* = 0 */) -> void {

		this->removeBuff( buffid,  bRemoveSingleOne /* = true */,  buffInstanceId /* = 0 */);
		});
	Event2().Subscribe("LivingAttrib_removeBuff", m_listenerLiving2);

	typedef ListenerFunctionRef<int*, std::string&> Listener3;
	m_listenerLiving3 = SANDBOX_NEW(Listener3, [&](int* mLastEquipment, std::string& m_LastWeaponUserdata) -> void {
		for (int i = 0; i < MAX_EQUIP_SLOTS; ++i) 
			mLastEquipment[i] = this->getEquipItem(EQUIP_SLOT_TYPE(i));

		if (this->getEquipGrid(EQUIP_WEAPON)) 
			m_LastWeaponUserdata = this->getEquipGrid(EQUIP_WEAPON)->userdata_str;
		});
	Event2().Subscribe("LivingAttrib_Equip", m_listenerLiving3);

	typedef ListenerFunctionRef<BackPackGrid* &, int, int&> Listener4;
	m_listenerLiving4 = SANDBOX_NEW(Listener4, [&](BackPackGrid* & igrid, int equipType , int& itemid) -> void {
		igrid = this->getEquipGrid(EQUIP_SLOT_TYPE(equipType));
		itemid = this->getEquipItem(EQUIP_SLOT_TYPE(equipType));
		});
	Event2().Subscribe("LivingAttrib_GetGrid", m_listenerLiving4);
	

	typedef ListenerFunctionRef<> Listener5;
	Listener5* listener5 = SANDBOX_NEW(Listener5, [&]() -> void {
		this->SyncWeaponEnchantEffect();
		});
	Event2().Subscribe("LivingAttrib_SyncWeaponEnchant", listener5);
}
void LivingAttrib::DestroyEvent2()
{
	SANDBOX_RELEASE(m_listenerBlockClipTrap);
	SANDBOX_RELEASE(m_listenerLiving1);
	SANDBOX_RELEASE(m_listenerLiving2);
	SANDBOX_RELEASE(m_listenerLiving3);
	SANDBOX_RELEASE(m_listenerLiving4);
}
void LivingAttrib::revive()
{
	ActorAttrib::revive();

	m_Oxygen = START_OXYGEN;
}

void LivingAttrib::clearrevive(int cleartype)
{
	clearBuff(true);

	if(cleartype == 0)
	{
		//dropEquipItems();
	}
	else if(cleartype == 3)
	{
		dropEquipItemsToChest(BLOCK_CHEST_NORMAL);
	}
	else if (cleartype == 4)
	{
		dropEquipItemsToJar(200420);
	}
}

void LivingAttrib::onDie()
{
	ActorAttrib::onDie();

	returnArrows(); //中箭返还

	m_Oxygen = 0;
	clearBuff(true);
}

void LivingAttrib::clearBuffAndStatus()
{
	clearBuff(true);
	m_StatusAddAttInfo.clear();
}

//mode:0进入,  1退出,   2tick
void LivingAttrib::execBuff(ActorBuff *buff, int mode)
{
	//可以把老的buff逻辑屏蔽了
	//bool isNew = isNewStatus();
	//if (isNew) {
		if (!buff || !buff->def)
		{
			return; 
		}
		if (mode == 2)
		{
			return; 
		}

		if (buff->def->BuffType != 2) 
		{
			//特效
			ActorBody *body = m_OwnerActor->getBody();
			if (body && buff->def->Status.ParticleID > 0)
			{
				auto def = GetDefManagerProxy()->getParticleDef(buff->def->Status.ParticleID);
				if (def)
				{
					if (mode == 0)
					{
						//客机有可能因为模型加载原因会第一次播放失败
						 auto world = m_OwnerActor->getWorld();
						if ((world && world->isRemoteMode())|| (!world))
						{//客机
							buff->effplayertype = m_OwnerActor->playMotion(def->EffectName.c_str(), 1);
						}
						else
						{
							m_OwnerActor->playMotion(def->EffectName.c_str(), 1);
						}
						
					}
					else if (mode == 1)
					{
						buff->effplayertype = true;
						m_OwnerActor->stopMotion(def->EffectName.c_str());
					}
				}
			}

			//声音
			if (buff->def->Status.SoundID > 0 || !buff->def->Status.strSoundID.empty())
			{
				std::string strSoundPath;
				if (buff->def->Status.SoundID > 0)
				{
					auto def = GetDefManagerProxy()->getSoundDef(buff->def->Status.SoundID);
					if(def)
						strSoundPath = def->SoundPath;
				}
				else
				{
					strSoundPath = buff->def->Status.strSoundID;
				}
				
				if (mode == 0) {
					if (buff->def->SoundType == 1)
					{
						auto sound = m_OwnerActor->getSoundComponent();
						if (sound)
						{
							sound->playSoundFollowActor(strSoundPath.c_str(), 1.0f, 1.0f, true);
						}
					}
					else
					{
						auto sound = m_OwnerActor->getSoundComponent();
						if (sound)
						{
							sound->playSoundFollowActor(strSoundPath.c_str(), 1.0f, 1.0f, false);
						}
					}
				}
				else if (mode == 1)
				{
					auto sound = m_OwnerActor->getSoundComponent();
					if (sound)
					{
						sound->stopSoundFollowActor(strSoundPath.c_str());
					}
				}
			}

			//判断下是否为主机
			if (GetClientInfoProxy()->getMultiPlayer() != 2)
			{
				//新效果 在指定的buff结束/开始时 产生掉落物 code-by:曹泽港
				std::vector<StatusAttInfo> dropInfos;
				if (mode == 0)
				{
					getStatusAddAttInfo(BUFFATTRT_STATE__BRGIN_DROP,dropInfos);
				}
				else if (mode == 1)
				{
					getStatusAddAttInfo(BUFFATTRT_STATE_END_DROP,dropInfos);
				}
				if (dropInfos.size() > 0)
				{
					auto sound = m_OwnerActor->getSoundComponent();
					auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
					for (unsigned int i = 0; i < dropInfos.size(); i++)
					{
						const StatusAttInfo& info = dropInfos[i];
						if (info.vValue.size() >= 3)
						{
							int needBuff = info.vValue[0].value;
							if (needBuff == buff->buffid * 1000 + buff->bufflv)
							{
								if (dropComponent)
								{
									dropComponent->dropItem(info.vValue[1].value, 1);
								}
								SoundDef* def = GetDefManagerProxy()->getSoundDef(info.vValue[2].value);
								if (def != NULL)
								{
									if (sound)
									{
										sound->playSoundByTrigger(def->SoundPath.c_str(), 1.0f, 1.0f, false, true);
									}
								}
							}
						}
					}
				}
			}
		}

		//auto world = m_OwnerActor->getWorld();
		//if (world && !world->isRemoteMode()) {
			if (mode == 0)
				addStatusEffects(buff);
			else if (mode == 1)
				removeStatusEffects(buff);
		//}
	//}
#if 0
	else 
	{
		if (mode != 2 || m_OwnerActor->getWorld() != NULL && !m_OwnerActor->getWorld()->isRemoteMode())
		{
			if (buff->def->ScriptName[0])
			{
				callBuffScript(buff, mode);
			}
			else
			{
				setBuffAttrs(buff, mode);
			}
		}

		ActorBody *body = m_OwnerActor->getBody();
		if (body && !buff->def->EffectName.empty())
		{
			if (mode == 0 || mode == 3) 
				m_OwnerActor->playMotion(buff->def->EffectName, 1);
			else if (mode == 1) 
				m_OwnerActor->stopMotion(buff->def->EffectName);
		}

		if (!buff->def->SoundName.empty())
		{
			auto sound = m_OwnerActor->getSoundComponent();
			if (mode == 0)
			{
				if (sound)
				{
					sound->playSound(buff->def->SoundName, 1.0f, 1.0f);
				}
			}
			else if (mode == 2)
			{
				if (buff->def->SoundType == 1)
				{
					if (sound)
					{
						sound->playSound(buff->def->SoundName, 1.0f, GenRandomFloat()*0.2f + 0.8f);
					}
				}
			}
		}

		if (body && buff->def->BodyModel > 0)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(m_OwnerActor);
			if (player)
			{
				if (mode == 0 || mode == 3)
				{
					player->changePlayerModel(body->getPlayerIndex(), buff->def->BodyModel, NULL);
				}
				else if (mode == 1)
				{
					player->changePlayerModel(body->getPlayerIndex(), 0, player->getCustomjson());
				}
			}
		}

		if (body && buff->def->PartScale)
		{
			if (mode == 0 || mode == 3)
			{
				body->setHeadBoneScale((float)buff->def->PartScale / 100.0f);
			}
			else if (mode == 1)
			{
				body->setHeadBoneScale(1.0f);
			}
		}
	}
#endif
}

const int StatusContinueAtt[] =
{
	BuffAttrType::BUFFATTRT_CONTINUE_CHG_HUNGER,
	BuffAttrType::BUFFATTRT_CONTINUE_CHG_HP,
	BuffAttrType::BUFFATTRT_CONTINUE_REDUCE_HP,
	BuffAttrType::BUFFATTRT_CONTINUE_CHG_STRENGTH,
	BuffAttrType::BUFFATTRT_TIME_DROP,
	BuffAttrType::BUFFATTRT_ADDITIONAL_STATUS,
	BuffAttrType::BUFFATTRT_CONTINUE_CHG_PER_HP,
};
void LivingAttrib::execStatusEffect(ActorBuff* status, int buffindex)
{
	if (m_OwnerActor->needClear() || isDead() || m_StatusAddAttInfo.size() == 0 || !status) { return; }
	auto world = m_OwnerActor->getWorld();
	if (!world || world->isRemoteMode()) { return; }

	int iSize = sizeof(StatusContinueAtt) / sizeof(int);
	for (int i = 0; i < iSize; i++)
	{
		auto it = m_StatusAddAttInfo.find(StatusContinueAtt[i]);
		if (it != m_StatusAddAttInfo.end()) {
			for (int j = 0; j < (int)it->second.size(); j++) {
				/*
				//if(m_hasClearBuffs)
				{
					return;//下面的函数会把m_Buffs 清空 导致 status野掉? code by :lihuimiao
				}
				*/
				StatusAttInfo& info = it->second[j];
				if (status && (info.iStatusId != status->buffid || info.iStatusIdx != status->buffidx)) { continue; }

				if (it->first == BuffAttrType::BUFFATTRT_CONTINUE_REDUCE_HP) {
					if (info.vValue.size() > 2) {
						int ticks = (int)(info.vValue[0].value * 20);
						if (info.vValue[0].iType == 3) { ticks /= 10; }
						if (ticks > 0 && status  &&  status->count%ticks == 0) {
							auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef((int)(info.vValue[2].value));
							if (!enumdef) { continue; }

							int iAtkType = enumdef->AttType - BuffAttrType::BUFFATTRT_MELEE_HURT;
							if (enumdef->AttType == BuffAttrType::BUFFATTRT_FIXED_HURT) {
								iAtkType = ATTACK_TYPE::ATTACK_FIXED;
							}
							else if (enumdef->AttType == BuffAttrType::BUFFATTRT_ICE_PROPERTY) {
								iAtkType = ATTACK_TYPE::ATTACK_ICE;
							}
							int hurtValue = info.vValue[1].value;
							//20220712 玩家和坐骑受到沙尘暴debuff时判断是否有骆驼坐骑减伤
							if (status->buffid == DUSTSTORM_BUFF)
							{
								hurtValue = getHurtValueOnLuotuo(hurtValue);
							}

							attackedByBuff(iAtkType, hurtValue, status->fromObjid, status->buffid, status->bufflv);
							//attackedByBuff(iAtkType, info.vValue[1].value, status->fromObjid);

							if (m_OwnerActor->needClear() || isDead()) { return; }
						}
					}
				}
				else if (it->first == BuffAttrType::BUFFATTRT_ADDITIONAL_STATUS)
				{
					if (info.vValue.size() > 1)
					{
						int ticks = (int)(info.vValue[0].value * 20);
						// 经过策划沟通，这里神经麻痹时长要除以10
						if (/*status->buffid == 1016 && */info.vValue[1].value == 1017001) {
							ticks /= 10;
						}
						//if (info.vValue[0].iType == 3) { ticks /= 10; }
						if (ticks > 0 && status->count % ticks == 0)
						{
							//addBuff(info.vValue[1].value / 1000, 1);
							//int iRealStatusId = GetDefManagerProxy()->getRealStatusId(info.vValue[1].value / 1000, 1);
							//auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
							//ActorBuff ststatus;
							//ststatus.buffid = info.vValue[1].value / 1000;
							//ststatus.bufflv = 1;
							//ststatus.def = def;
							//ststatus.ticks = def->Status.LimitTime * 20;
							//execBuff(&ststatus, 0);

							int buffid = info.vValue[1].value / 1000;
							m_tempBuffsMap[buffid] = 1;
						}
					}
				}
				else {
					if (info.vValue.size() > 1) {
						int ticks = (int)(info.vValue[0].value * 20);
						if (info.vValue[0].iType == 3) { ticks /= 10; }
						bool needUpdate = false;
						bool immediately = false;
						//立即执行
						if (info.vValue.size() > 2)
						{
							if (info.vValue[2].iParaType > 0 && info.vValue[2].iType == 0 && info.vValue[2].value == 1.0f)
							{
								immediately = true;
							}
						}

						if (!immediately && (ticks > 0 && status->count % ticks == 0))
						{
							needUpdate = true;
						}
						else if(immediately &&(ticks > 0 && status->count % ticks == 1))//立即执行
						{
							needUpdate = true;
						}
						if (needUpdate) {
							if (it->first == BuffAttrType::BUFFATTRT_CONTINUE_CHG_HUNGER) {
								PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
								if (pAtt) {
									int lv = (int)(pAtt->getFoodLevel() + info.vValue[1].value);
									pAtt->setFoodLevel(lv);
								}
							}
							else if (it->first == BuffAttrType::BUFFATTRT_CONTINUE_CHG_HP) {
								addHP(info.vValue[1].value);
							}
							//添加新效果 code-by:曹泽港
							else if (it->first == BuffAttrType::BUFFATTRT_CONTINUE_CHG_STRENGTH)
							{
								PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
								if (pAtt)
								{
									pAtt->addStrength(info.vValue[1].value);
								}
							}
							else if (it->first == BuffAttrType::BUFFATTRT_CONTINUE_CHG_PER_HP)
							{
								int max = getMaxHP();
								float pre = info.vValue[1].value;
								int hp =(max * pre)/100;
								addHP(hp);
							}
							else if (it->first == BuffAttrType::BUFFATTRT_TIME_DROP)
							{
								auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
								if (dropComponent)
								{
									dropComponent->dropItem((int)(info.vValue[1].value), 1);
								}
								SoundDef* def = GetDefManagerProxy()->getSoundDef((int)(info.vValue[2].value));
								if (def != NULL)
								{
									auto sound = m_OwnerActor->getSoundComponent();
									if (sound)
									{
										sound->playSoundByTrigger(def->SoundPath.c_str(), 1.0f, 1.0f, false, true);
									}
								}
							}
						}
					}
				}

				//检测buff数据是否还有效
				auto addAttIter = m_StatusAddAttInfo.find(StatusContinueAtt[i]);
				if (addAttIter == m_StatusAddAttInfo.end())
					return;

				if (buffindex >= m_Buffs.size())
					return;
			}
		}
	}
}

//吸收伤害
float LivingAttrib::manageShieldLife(float damage)
{
	if (/*!isNewStatus() || */damage <= 0) { return damage; }
	
	float fValue = damage;
	auto it = m_StatusAddAttInfo.find(BuffAttrType::BUFFATTRT_DAMAGE_ABSORB);
	if (it != m_StatusAddAttInfo.end()) {
		for (int i = 0; i < (int)it->second.size(); i++) {
			if (it->second[i].vValue.size() > 0) {
				if (it->second[i].vValue[0].iType != 1) {
					fValue -= it->second[i].vValue[0].value;
					if (fValue >= 0.0f) {
						it->second[i].vValue[0].value = 0.0f;
					}
					else {
						it->second[i].vValue[0].value = -fValue;
						fValue = 0.0f;
						break;
					}
				}
			}
		}
	}

	return fValue;
}

bool LivingAttrib::hasBuff(int buffid, int bufflv)
{
	for (size_t i = 0; i < m_Buffs.size(); i++)
	{
		if (m_Buffs[i].buffid == buffid)
		{
			if (bufflv <= 0) 
			{ 
				return true; 
			}
			else if (bufflv == m_Buffs[i].bufflv)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
			
	}
	return false;
}

bool LivingAttrib::HasBadBuff()
{
	return m_hasBadBuff;
}

int  LivingAttrib::getBuffIndex(int buffid)
{
  	for(size_t i=0; i<m_Buffs.size(); i++)
	{
		if(m_Buffs[i].buffid == buffid)
		{
			return i;
		}
	}
	return -1;
}

bool LivingAttrib::hasBuffByNature(int nature)
{
	//bool isNew = isNewStatus();
	//if (isNew) {
		for (size_t i = 0; i < m_Buffs.size(); i++) {
			if (GetDefManagerProxy()->isCustomStatus(m_Buffs[i].buffid)) { continue; }

			if (m_Buffs[i].def && m_Buffs[i].def->Nature == nature) {
				return true;
			}
			if (m_Buffs[i].def && (m_Buffs[i].def->ID / 1000 == SCALD_BUFF) && nature == 1)
			{
				return true;
			}
		}
	//}
	//else {
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		const BuffDef *def = GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv);
	//		if (def == NULL)
	//		{
	//			LOG_SEVERE("cannot find buff: id=%d, lv=%d", m_Buffs[i].buffid, m_Buffs[i].bufflv);
	//		}
	//		if (def && def->Nature == nature) return true;
	//		if (def && (m_Buffs[i].def->ID / 1000 == SCALD_BUFF) && nature == 1)
	//		{
	//			return true;
	//		}
	//	}
	//}
	
	return false;
}

void LivingAttrib::addBuffTimeExtended(int buffid, int bufflv, float add_ratio)
{
	if(add_ratio == 0) addBuff(buffid, bufflv);
	else
	{
		//bool isNew = isNewStatus();
		//if (isNew) {
			int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffid, bufflv);
			auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
			if (!def) { return; }

			addBuff(buffid, bufflv, (int)(def->Status.LimitTime*(1.0f + add_ratio)));
		//}
		//else {
		//	const BuffDef *def = GetDefManagerProxy()->getBuffDef(buffid, bufflv);
		//	addBuff(buffid, bufflv, int(def->EffectTicks*(1.0f + add_ratio)));
		//}
	}
}

void LivingAttrib::addBuff(int buffid, int bufflv, int customticks/* =-1 */, int buffInstanceId/* =0 */, long long objid/* = 0*/)
{
	if (m_OwnerActor->needClear() || isDead()) return;

	if (!m_OwnerActor->IsPotionApplicable(buffid))
	{
		return;
	}

	if (isHasAttrShapeShiftBuff(buffid, bufflv))
		return;

	if (hasBuff(1022))//荧光棒藻鱼无敌时不受别的buff影响
		return;

	if (hasBuff(INVULNERABLE_BUFF) && (1037 == buffid || 1033 == buffid)) //无敌的时候不添加冻结buff
		return;

	GetOwner()->Event2().Emit<int>("hitActorVillager", buffid);
	bool bAdd = true;
	//bool isNew = isNewStatus();
	//if (isNew) 
	//{
		int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffid, bufflv);
        if (CheckImmueBuff(iRealStatusId)) {
            return;
        }
		bool isEquip = GetDefManagerProxy()->isEquipStatus(iRealStatusId);
		if (isEquip) 
		{
			auto world = m_OwnerActor->getWorld();
			if (world && world->isRemoteMode()) 
			{//客机
				int iEquipId = bufflv;//如果是装备状态时，bufflv就是装备ID
				GetDefManagerProxy()->addEquipStatus(iEquipId);
			}
		}

		auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
		if (!def)
		{
			def = GetDefManagerProxy()->getToolStatusDef(iRealStatusId);
		}
		if (!def)
		{
			return;
		}

		if (customticks < 0)
		{
			customticks = def->Status.LimitTime * 20;
		}
		if (customticks == 0)
		{
			customticks = 9999 * 20; 
		}

		bool isCustomStatus = GetDefManagerProxy()->isCustomStatus(iRealStatusId);
		for (size_t i = 0; i < m_Buffs.size(); i++) 
		{
			if (!m_Buffs[i].def || m_Buffs[i].def->BuffType != 1)
			{
				continue;
			}//不是状态类型

			if ((isCustomStatus && iRealStatusId == m_Buffs[i].buffid) || buffid == m_Buffs[i].buffid)
			{
				int changeModelCopyIdIdx = -1;
				for (int j = 0; j < MAX_BUFF_ATTRIBS; j++)
				{
					if (def->Status.EffInfo[j].CopyID == 1016)  //如果是改变外观的效果
					{
						changeModelCopyIdIdx = j;
						break;
					}
				}

				if (isCustomStatus)
				{
					if (def->Status.Priority == 1)
					{ //覆盖，如果是覆盖属性 同一状态id的状态 不会大于1
						ActorBuff stTmpStatus = m_Buffs[i];
						stTmpStatus.ticks = customticks;
						stTmpStatus.fromObjid = objid;
						m_Buffs.erase(m_Buffs.begin() + i);
						m_Buffs.push_back(stTmpStatus);

						if (changeModelCopyIdIdx != -1)
						{
							addOneEffect(&m_Buffs[i], def->Status.EffInfo[changeModelCopyIdIdx]);
						}
						static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(0, iRealStatusId, 1, customticks, stTmpStatus.buffidx);
						return;
					}
				}
				else
				{
					if (def->Level < m_Buffs[i].def->Level && changeModelCopyIdIdx == -1) 
					{//新增状态等级小于身上的状态等级则不添加
						return;
					}
					else if (def->Level >= m_Buffs[i].def->Level)
					{//改变状态的时间、等级和结构
						bool isDiff = def->Level > m_Buffs[i].def->Level;
						if (isDiff)
						{
							execBuff(&m_Buffs[i], 1);
						}
						ActorBuff stTmpStatus = m_Buffs[i];
						stTmpStatus.ticks = customticks;
						stTmpStatus.bufflv = def->Level;
						stTmpStatus.def = def;
						stTmpStatus.fromObjid = objid;
						if (m_delegateBuffRemove && isDiff)
						{
							m_delegateBuffRemove(buffid, m_Buffs[i].def->Level);
						}
						m_Buffs.erase(m_Buffs.begin() + i);
						m_Buffs.push_back(stTmpStatus);
						if (m_delegateBuffAppend && isDiff)
						{
							m_delegateBuffAppend(buffid, def->Level);
						}
						if (isDiff)
						{
							execBuff(&stTmpStatus, 0);
						}
						else if (changeModelCopyIdIdx != -1)
						{
							addOneEffect(&m_Buffs[i], def->Status.EffInfo[changeModelCopyIdIdx]);
						}
						static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(0, buffid, bufflv, customticks, stTmpStatus.buffidx);
						return;
					}
					else if (changeModelCopyIdIdx != -1) // BuffID相同 等级1级变身 2级其他状态。 先获得2级再获得1级 ，倒计时结束1级状态不删除
					{
						ActorBuff stTmpStatus = m_Buffs[i];
						stTmpStatus.ticks = customticks;
						stTmpStatus.bufflv = def->Level;
						stTmpStatus.def = def;
						stTmpStatus.fromObjid = objid;
						m_Buffs.push_back(stTmpStatus);

						addOneEffect(&m_Buffs[i], def->Status.EffInfo[changeModelCopyIdIdx]);
						return;
					}
				}
			}
		}

		ActorBuff stStatus;
		stStatus.buffid = isCustomStatus ? iRealStatusId : buffid;
		stStatus.bufflv = (isCustomStatus && !isEquip) ? 1 : bufflv;
		stStatus.fromObjid = objid;
		if (buffInstanceId > 0)
			stStatus.buffidx = buffInstanceId;
		else
			stStatus.buffidx = m_iStatusIdx++;
		stStatus.ticks = customticks;
		stStatus.def = def;

		m_Buffs.push_back(stStatus);
		if (bAdd && m_delegateBuffAppend)
		{
			m_delegateBuffAppend(buffid, bufflv);
		}
		execBuff(&m_Buffs[m_Buffs.size() - 1], 0);
		/*m_Buffs[m_Buffs.size() - 1].effplayertype = stStatus.effplayertype;*/

		if (BOUND_BUFF == buffid)
		{
			LivingLocoMotion *loc = dynamic_cast<LivingLocoMotion *>(m_OwnerActor->getLocoMotion());
			if (loc)
				loc->setBoundJump(false);
		}

		static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(0, stStatus.buffid, stStatus.bufflv, customticks, stStatus.buffidx);
	//} 
	//else 
	//{
 //       if (CheckImmueBuff(buffid)) {
 //           return;
 //       }
	//	const BuffDef *def = GetDefManagerProxy()->getBuffDef(buffid, bufflv);
	//	if (def == NULL)
	//	{
	//		LOG_SEVERE("addBuff failed: buffid=%d, bufflv=%d", buffid, bufflv);
	//		return;
	//	}

	//	ActorBuff *pbuff = NULL;
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		if (m_Buffs[i].buffid == buffid)
	//		{
	//			if (m_Buffs[i].bufflv > bufflv)
	//			{
	//				return;
	//			}
	//			pbuff = &m_Buffs[i];
	//			break;
	//		}
	//	}

	//	if (customticks <= 0) customticks = def->EffectTicks;

	//	if (pbuff == NULL)
	//	{
	//		m_Buffs.resize(m_Buffs.size() + 1);
	//		pbuff = &m_Buffs.back();

	//		pbuff->def = def;
	//		pbuff->buffid = buffid;
	//		pbuff->bufflv = bufflv;
	//		pbuff->ticks = customticks;

	//		if (bAdd && m_delegateBuffAppend)
	//		{
	//			m_delegateBuffAppend(buffid, bufflv);
	//		}
	//		execBuff(pbuff, 0);
	//	}
	//	else
	//	{
	//		pbuff->bufflv = bufflv;
	//		pbuff->ticks = customticks;
	//	}

	//	pbuff->fromObjid = objid;

	//	if (BOUND_BUFF == buffid)
	//	{
	//		LivingLocoMotion *loc = dynamic_cast<LivingLocoMotion *>(m_OwnerActor->getLocoMotion());
	//		if (loc)
	//		{
	//			loc->setBoundJump(false);
	//		}
	//	}

	//	if (INVULNERABLE_BUFF == buffid)
	//	{
	//		m_OwnerActor->setInvulnerable(true);
	//	}

	//	static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(0, buffid, bufflv, customticks);
	//}
}

void LivingAttrib::addBuffOnLoad(int buffid, int bufflv, int ticks)
{
	LOG_INFO("addBuffOnLoad: %d, %d, %d", buffid, bufflv, ticks);
	addBuff(buffid, bufflv, ticks);

	//bool isNew = isNewStatus();
	//if (isNew) 
	//{
	//	addBuff(buffid, bufflv, ticks);
	//}
	//else 
	//{
	//	ActorBuff buff;
	//	buff.buffid = buffid;
	//	buff.bufflv = bufflv;
	//	buff.ticks = ticks;
	//	buff.def = GetDefManagerProxy()->getBuffDef(buff.buffid, buff.bufflv);

	//	if (buff.def)
	//	{
	//		m_Buffs.push_back(buff);
	//		execBuff(&buff, 3);
	//	}
	//}
}

void LivingAttrib::addEquipBuff(int iEquipId)
{
	int iStatusId = GetDefManagerProxy()->addEquipStatus(iEquipId);
	if (iStatusId > 0)
		addBuff(iStatusId, iEquipId);
}

void LivingAttrib::removeEquipBuff(int iEquipId)
{
	int iStatusId = GetDefManagerProxy()->getStatusIdByEquipId(iEquipId);
	if(iStatusId > 0)
		removeBuff(iStatusId);
}

bool LivingAttrib::hasEquipBuff(int iEquipId)
{
	int iStatusId = GetDefManagerProxy()->getStatusIdByEquipId(iEquipId);
	if(iStatusId > 0)
		return hasBuff(iStatusId);

	return false;
}

void LivingAttrib::addToolBuff(int iToolId)
{
	int iStatusId = GetDefManagerProxy()->addToolStatus(iToolId);
	if (iStatusId > 0)
		addBuff(iStatusId, iToolId);
}

void LivingAttrib::removeToolBuff(int iToolId)
{
	int iStatusId = GetDefManagerProxy()->getStatusIdByToolId(iToolId);
	if (iStatusId > 0)
		removeBuff(iStatusId);
}

bool LivingAttrib::hasToolBuff(int iToolId)
{
	int iStatusId = GetDefManagerProxy()->getStatusIdByToolId(iToolId);
	if (iStatusId > 0)
		return hasBuff(iStatusId);

	return false;
}

void LivingAttrib::removeBuff(int buffid, bool bRemoveSingleOne /* = true */, int buffInstanceId /* = 0 */)
{
	//bool isNew = isNewStatus();
	//if (isNew) 
	//{
		bool isCustomStatus = GetDefManagerProxy()->isCustomStatus(buffid);
		for (size_t i = 0; i < m_Buffs.size(); ) 
		{
			if (isCustomStatus) 
			{
				if (m_Buffs[i].buffid == buffid && m_Buffs[i].def) 
				{
					if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId) 
					{
						int instanceId = (m_Buffs[i].def->BuffType == 2 ? 0 : m_Buffs[i].buffidx);
						execBuff(&m_Buffs[i], 1);
						if (m_delegateBuffRemove)
						{
							m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
						}
						m_Buffs.erase(m_Buffs.begin() + i);
						
						static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, buffInstanceId);
						if (bRemoveSingleOne)//删除单个，默认删除vector的第一个
							break;
					}
					else
					{
						i++; 
					}
				}
				else 
				{
					i++; 
				}
			}
			else 
			{//老的状态不会同时存在多个
				if (m_Buffs[i].buffid == buffid) 
				{
					if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId) 
					{
						int instanceId = m_Buffs[i].buffidx;
						execBuff(&m_Buffs[i], 1);
						if (m_delegateBuffRemove)
						{
							m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
						}
						m_Buffs.erase(m_Buffs.begin() + i);

						static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, buffInstanceId);
						break;
					}
					else
					{
						i++; 
					}
				}
				else
				{
					i++; 
				}
			}
		}
	//}
	//else 
	//{
	//	size_t i = 0;
	//	for (; i < m_Buffs.size(); i++)
	//	{
	//		if (m_Buffs[i].buffid == buffid)
	//		{
	//			break;
	//		}
	//	}

	//	if (i == m_Buffs.size()) return;

	//	execBuff(&m_Buffs[i], 1);

	//	if (i + 1 < m_Buffs.size()) m_Buffs[i] = m_Buffs.back();
	//	if (m_delegateBuffRemove)
	//	{
	//		m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
	//	}
	//	m_Buffs.resize(m_Buffs.size() - 1);

	//	if (buffid == INVULNERABLE_BUFF)
	//	{
	//		m_OwnerActor->setInvulnerable(false);
	//	}

	//	static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0);
	//}

	//removeBuffEffect(buffid);
}

void LivingAttrib::removeBuffByIndex(int idx)
{
	if (idx < 0 || idx >= (int)(m_Buffs.size())) { return; }

	int buffid = m_Buffs[idx].buffid;
	int instanceId = m_Buffs[idx].buffidx;
	execBuff(&m_Buffs[idx], 1);
	if (m_delegateBuffRemove)
	{
		m_delegateBuffRemove(buffid, m_Buffs[idx].bufflv);
	}
	m_Buffs.erase(m_Buffs.begin() + idx);
	//重新生效buf特效、音效
	againTakeBuffEffect();
	againTakeBuffSound();
	static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);

	//removeBuffEffect(buffid);
}

void LivingAttrib::removeBuffByNature(int nature)
{
	int ids[64];
	int count = 0;
	//bool isNew = isNewStatus();
	//if (isNew) 
	//{
		for (size_t i = 0; i < m_Buffs.size(); i++) 
		{
			if (!GetDefManagerProxy()->isCustomStatus(m_Buffs[i].buffid))
			{//只有老的状态才会有nature属性
				if (m_Buffs[i].def && m_Buffs[i].def->Nature == nature) 
				{
					ids[count++] = m_Buffs[i].buffid;
				}
			}
		}
	//}
	//else
	//{
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		if (GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv)->Nature == nature)
	//		{
	//			ids[count++] = m_Buffs[i].buffid;
	//		}
	//	}
	//}

	for(int i=0; i<count; i++)
	{
		removeBuff(ids[i], false);
	}
}

void LivingAttrib::clearRandomBuff()
{
	int ids[64];
	int count = 0;

	/*if (m_Buffs.size() > 0)
	{
		int i = GenRandomInt(m_Buffs.size());
		removeBuffByIndex(i);
	}*/

	//bool isNew = isNewStatus();
	//if (isNew) {
		for (size_t i = 0; i < m_Buffs.size(); i++) {
            int buffid = GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv);
            if (buffid == 97001 || buffid == 93001 || buffid == 93002 || buffid == 1023001/*水草缠绕*/)
			{
				continue;
			}
			ids[count++] = m_Buffs[i].buffid;
			
		}
	//}
	//else {
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		if (GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv)) ids[count++] = m_Buffs[i].buffid;
	//	}
	//}

	if (count > 0)
	{
		int i = GenRandomInt(count);
		removeBuff(ids[i]);
	}
}

void LivingAttrib::clearRandomBadBuff()
{
	int ids[64];
	int count = 0;

	//bool isNew = isNewStatus();
	//if (isNew) {
		for (size_t i = 0; i < m_Buffs.size(); i++) {
			// 光之力冷却buff不可消除暂时特殊处理，下一个版本出方案 code-by: liya
			if (m_Buffs[i].def && m_Buffs[i].def->Type == 1)
            {
                int buffID = GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv);
                //1023001:水草缠绕BUFF，不可被清除
                if (buffID == 97001 || buffID == 1023001)
                {
                    continue;
                }
				ids[count++] = m_Buffs[i].buffid;
			}
		}
	//}
	//else {
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		if (GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv)->Type == 1) ids[count++] = m_Buffs[i].buffid;
	//	}
	//}

	if (count > 0)
	{
		int i = GenRandomInt(count);
		removeBuff(ids[i]);
	}
}

void LivingAttrib::clearBuffByFoodDef(const FoodDef* def)
{
    if (def == nullptr) {
        return;
    }

    for (size_t i = 0; i < m_Buffs.size(); i++) {
        for (size_t j = 0; j < MAX_CLEAR_BUFF; j++) {
            if (def->ClearBuffID[j] == 0 || def->ClearBuffID[j] != GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv)) {
                continue;
            }
            removeBuff(m_Buffs[i].buffid);
        }
    }
}

void LivingAttrib::clearAllBadBuff()
{
	int buffids[128];
	int count = 0;
	//bool isNew = isNewStatus();
	//if (isNew) {
		for (size_t i = 0; i < m_Buffs.size(); i++) {
			// 光之力冷却buff不可消除暂时特殊处理，下一个版本出方案 code-by: liya
			if (m_Buffs[i].def && m_Buffs[i].def->Type == 1)
			{
				int buffID = GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv);
				//1023001:水草缠绕BUFF，不可被清除
				if (buffID == 97001 || buffID == 1023001)
				{
					continue;
				}
				buffids[count++] = m_Buffs[i].buffid;
			}
		}
	//}
	//else {
	//	for (size_t i = 0; i < m_Buffs.size(); i++)
	//	{
	//		if (GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv)->Type == 1) buffids[count++] = m_Buffs[i].buffid;
	//	}
	//}

	for(int i=0; i<count; i++) removeBuff(buffids[i], false);
}

void LivingAttrib::clearBuff(bool bUseClearFlag /* = false */)
{
	m_hasClearBuffs = true;
	//bool isNew = isNewStatus();
	//if (isNew) {
		if (bUseClearFlag) {
			vector<int> buffids;
			int count = 0;
			for (size_t i = 0; i < m_Buffs.size(); i++) {
				if (m_Buffs[i].def && m_Buffs[i].def->Status.DeathClear == 1) {
					buffids.push_back(m_Buffs[i].buffid);
					count++;
				}
			}

			if (count == m_Buffs.size()) {
				clearBuff();
			}
			else {
				for (int i = 0; i < count; i++)
				{
					removeBuff(buffids[i], false);
					removeBuffEffect(buffids[i]);
				}
			}
		}
		else {
			int buffid;
			for (size_t i = 0; i < m_Buffs.size(); i++) {
				buffid = m_Buffs[i].buffid;
				execBuff(&m_Buffs[i], 1);
				if (m_delegateBuffRemove)
				{
					m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
				}
				removeBuffEffect(buffid);
			}

			m_Buffs.clear();
			
			static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(2, 0, 0, 0);
		}
	/*}
	else {
		int buffid;
		for (size_t i = 0; i < m_Buffs.size(); i++)
		{
			buffid = m_Buffs[i].buffid;
			execBuff(&m_Buffs[i], 1);

			if (m_Buffs[i].buffid == INVULNERABLE_BUFF)
			{
				m_OwnerActor->setInvulnerable(false);
			}
			if (m_delegateBuffRemove)
			{
				m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
			}

			removeBuffEffect(buffid);
		}

		m_Buffs.resize(0);
		static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(2, 0, 0, 0);
	}*/
}

int LivingAttrib::getBuffNum()
{
	return m_Buffs.size();
}

int LivingAttrib::getBuffNumById(int buffid)
{
	int num = 0;
	for (auto iter = m_Buffs.begin(); iter != m_Buffs.end(); iter++)
	{
		if (iter->buffid == buffid)
			++num;
	}
	return num;
}

ActorBuff LivingAttrib::getBuffInfo(int index)
{
	assert(index < getBuffNum());	
	if(getBuffNum() == 0){
		ActorBuff buff;
		buff.buffid = 6;
		buff.bufflv = 1;
		buff.ticks = 60;
		buff.def = GetDefManagerProxy()->getStatusDef(buff.buffid * 1000 + buff.bufflv);
		//if (isNewStatus()) {
		//	buff.def = GetDefManagerProxy()->getStatusDef(buff.buffid * 1000 + buff.bufflv);
		//}
		//else {
		//	buff.def = GetDefManagerProxy()->getBuffDef(buff.buffid, buff.bufflv);
		//}
	
		return buff;
	}
	if(index >= getBuffNum() || index < 0) return m_Buffs[0];
	return m_Buffs[index];
}

void LivingAttrib::RefreshAllBuffTick()
{
	for (unsigned int i = 0; i < m_Buffs.size(); i++)
	{
		ActorBuff& stBuff = m_Buffs[i];
		if (stBuff.ticks == gBuffTickMax || !stBuff.def)
			continue;

		stBuff.ticks = stBuff.def->Status.LimitTime * 20;
	}
}

void LivingAttrib::SetBuffTick(int buffid, int bufflv, int ticks)
{
	//bool isNew = isNewStatus();
	if (/*!isNew || */GetDefManagerProxy()->isCustomStatus(buffid)) { return; }

	for(size_t i=0; i<m_Buffs.size(); i++)
	{
		if(m_Buffs[i].buffid == buffid)
		{
			m_Buffs[i].ticks = ticks;
			static_cast<ActorLiving *>(m_OwnerActor)->onBuffChange(0, buffid, bufflv, ticks, m_Buffs[i].buffidx);

			return;
		}
	}
}

void LivingAttrib::AddBuffTick(int buffId, int ticks)
{
	for (size_t i = 0; i < m_Buffs.size(); i++)
	{
		if (m_Buffs[i].buffid == buffId)
		{
			m_Buffs[i].ticks += ticks;
			if (m_Buffs[i].ticks < 0)
			{
				m_Buffs[i].ticks = 0;
			}
			return;
		}
	}
}

bool LivingAttrib::isActionDisabled()
{
#if 0
	bool isNew = isNewStatus();
	if (isNew)
	{
		std::vector<StatusAttInfo> vValue;
		getStatusAddAttInfo(BuffAttrType::BUFFATTRT_MODEL_CHANGE, vValue);
		for (int i = 0; i < (int)vValue.size(); i++)
		{
			if (vValue[i].vValue.size() >= 3 && vValue[i].vValue[2].value)
				return true;
		}
	}
	else {
		for (size_t i = 0; i < m_Buffs.size(); i++)
		{
			const BuffDef *def = GetDefManagerProxy()->getBuffDef(m_Buffs[i].buffid, m_Buffs[i].bufflv);
			if (def && def->DisableAction) return true;
		}
	}
#else
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BuffAttrType::BUFFATTRT_MODEL_CHANGE, vValue);
	for (int i = 0; i < (int)vValue.size(); i++)
	{
		if (vValue[i].vValue.size() >= 3 && vValue[i].vValue[2].value)
			return true;
	}
#endif
	
	return false;
}

void LivingAttrib::addOxygen(float v)
{
	if (/*isNewStatus() && */dynamic_cast<ClientPlayer *>(m_OwnerActor) && v < 0.0f)
	{
		auto func = [](const std::vector<StatusAttInfo>& vValue, float baseValue) -> float {
			float fBuffValue = 0.0f;
			bool isOperation = false;
			for (int i = 0; i < (int)vValue.size(); i++) 
			{
				//百分比 TODO
				if (!Rainbow::FloatIsZero(vValue[i].vValue[0].value))
				{
					if (vValue[i].vValue.size() >= 2)
					{
						if (vValue[i].vValue[1].iType == 1)
						{
							fBuffValue += vValue[i].vValue[1].value / vValue[i].vValue[0].value;
							isOperation = true;
						}
						else if (vValue[i].vValue[1].iType == 3)
						{
							fBuffValue += vValue[i].vValue[1].value * 10.0f / vValue[i].vValue[0].value;
							isOperation = true;
						}
						else
						{
							fBuffValue += vValue[i].vValue[1].value / vValue[i].vValue[0].value;
							isOperation = true;
						}
					}
				}
			}
			//如果没运算过说明没有这个效果
			if (!isOperation) return baseValue;

			return fBuffValue;
		};

		World *pworld = m_OwnerActor ? m_OwnerActor->getWorld() : NULL;
		LivingLocoMotion *locoMotion = dynamic_cast<LivingLocoMotion *>(m_OwnerActor->getLocoMotion());
		if (pworld && locoMotion)
		{
			std::vector<StatusAttInfo> vValue;
			if (locoMotion->m_InWater)
			{
				//水中
				getStatusAddAttInfo(BuffAttrType::BUFFATTRT_ENV_INWATER, vValue);
				float value = func(vValue, v);
				//code-by:hanyunqiang//野外求生 水下呼吸时间增加xx%
				float geniadd = getGeniusValue(GENIUS_SURVIVE, 2);
				value *= (1.0f-geniadd);


				//m_Oxygen += value;
				//  公式：当前氧气消耗速度=氧气消耗速度*(1+(水压等级-1)/系数)
				int waterpress = getCalculatedWaterPressure();
				if (waterpress > 0)
				{
					float coe = GetLuaInterfaceProxy().get_lua_const()->oxygen_consume_coefficient;
					if (coe == 0) coe = 1.0f;
					value *= (1.0f + (waterpress - 1) / coe);
				}


				if (getAttrShapeShift() && m_AttrShapeShiftDef)
				{
					// 属性变身后特性处理
					if (m_AttrShapeShiftDef->ID == 3224)
					{
						value = 0;
						if (m_Oxygen <= 0)
							m_Oxygen += 0.1f;
					}
				}

				//在水中如果装备了氧气背包则优先消耗氧气背包内的氧气，氧气背包消耗完了装备销毁了才消耗角色的氧气
				if (value < 0 && damageOxygenpack(std::abs(value)))
				{
					value = 0;
				}

				m_Oxygen += value;
			}
			else if (locoMotion->isInSpaceAirBlock())
			{
				//太空
				getStatusAddAttInfo(BuffAttrType::BUFFATTRT_ENV_SPACE, vValue);
				float value = func(vValue, v);
				m_Oxygen += value;
			}
			else
			{
				m_Oxygen += v;
			}
		}
		else
		{
			m_Oxygen += v;
		}
	}
	else
	{
		m_Oxygen += v;
	}

	if(m_Oxygen < 0)
	{
		m_Oxygen = 0;
	}
	else if (m_Oxygen > START_OXYGEN)
	{
		m_Oxygen = START_OXYGEN;
	}
}

void LivingAttrib::tick()
{
	ActorAttrib::tick();
	m_Tick++;

	//bool isNew = isNewStatus();
	
	bool hasBadBuff = false;
	if (m_Buffs.empty())
	{
		m_lastAttackBuffLv = 0;
		m_lastAttackBuffId = 0;
	}
	
	// 缓冲区加入buff队列
	if (!m_tempBuffsMap.empty())
	{
		for (auto itr = m_tempBuffsMap.begin(); itr != m_tempBuffsMap.end(); itr++)
		{
			addBuff(itr->first, itr->second);
		}

		m_tempBuffsMap.clear();
	}
	
	size_t i = 0;
	while( i < m_Buffs.size() )
	{
		ActorBuff& buff = m_Buffs[i];
		buff.count++;
		if (buff.count >= gBuffTickMax)
		{
			buff.count = 1;
		}

		if (buff.ticks != gBuffTickMax)
		{//9999*20
			if (buff.ticks <= 0)
			{
				int nSize = m_Buffs.size();
				removeBuffByIndex(i);

				//防止死循环
				if (nSize == m_Buffs.size())
				{
					++i;
				}
				continue;
			}
			else
			{
				execStatusEffect(&m_Buffs[i], i);
			}

			if (i < m_Buffs.size() && (&buff == &m_Buffs[i])) // buff可能被删除, 防止崩溃 2024.03.05 by huanglin
				--buff.ticks;			
		}
		else
		{
			execStatusEffect(&m_Buffs[i], i);
		}
		//客机执行失败了重新尝试播放
		if (!buff.effplayertype)
		{
			if (buff.def->BuffType != 2)
			{
				//特效
				ActorBody* body = m_OwnerActor->getBody();
				if (body && buff.def->Status.ParticleID > 0)
				{
					auto def = GetDefManagerProxy()->getParticleDef(buff.def->Status.ParticleID);
					if (def)
					{
							//客机有可能因为模型加载原因会第一次播放失败
							auto world = m_OwnerActor->getWorld();
							if (world && world->isRemoteMode())
							{//客机
								buff.effplayertype = m_OwnerActor->playMotion(def->EffectName.c_str(), 1);
							}
					}
				}
			}
		}
		if ((i < m_Buffs.size() && (&buff == &m_Buffs[i])) && buff.def->Type == 1) // buff可能被删除, 防止崩溃 2024.03.05 by huanglin
		{
			hasBadBuff = true;
		}
		++i;
	}
	
	for (auto& equipEffec : m_EquipBuffs)
	{
		for (auto& actorBuff : equipEffec.entryBuffs)
		{
			execStatusEffect(&actorBuff, 0);
		}
	}

	m_hasBadBuff = hasBadBuff;

	if ( 0 < m_RemoveBuffIds.size())
	{
		for (int buffId : m_RemoveBuffIds)
		{
			removeBuff(buffId, false);
		}
		m_RemoveBuffIds.clear();
	}

	// temperatureTick();
	if (g_WorldMgr && !g_WorldMgr->isRemote())
	{
		randomCreateBlockTick();
		changeAroundLiquidTick();
	}

	auto pActor = GetOwnerActor();
	if (!pActor)
		return;

	int iObjType = pActor->getObjType();
	if (iObjType != OBJ_TYPE_DROPITEM && iObjType != OBJ_TYPE_EXPORB && iObjType != OBJ_TYPE_GAMEOBJECT)
	{
		if (getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			if (pActor->getFlying())
			{
				ClientPlayer* player = dynamic_cast<ClientPlayer*>(GetOwnerPlayer());
				if (player)
					player->setFlyingAndSync(false);
				else
					pActor->ToCast<ClientActor>()->setFlying(false);
			}
		}
	}
}

void LivingAttrib::callBuffScript(ActorBuff *buff, int mode)
{
	static const char *funcnames[] = {"_Start", "_End", "_Update"};

	if(mode == 3) return;

	assert(mode>=0 && mode<3);
	char fullname[256];
	sprintf(fullname, "%s%s", buff->def->ScriptName.c_str(), funcnames[mode]);

	if(mode==0 || mode==1)
	{
		MINIW::ScriptVM::game()->callFunction(fullname, "u[LivingAttrib]u[BuffDef]", this, buff->def);
	}
	else if(mode == 2) MINIW::ScriptVM::game()->callFunction(fullname, "u[LivingAttrib]u[BuffDef]i", this, buff->def, buff->ticks);
}

void LivingAttrib::setBuffAttrs(ActorBuff *buff, int mode)
{
	if (mode == 2)
	{
		return;
	}
	if (mode == 0 || mode == 3)
	{
		for (int i = 0; i < buff->def->NumAttr; i++)
		{
			MODATTRIB_TYPE attrtype = buff->def->AttrTypes[i];
			float attrval = buff->def->AttrValues[i];
			addModAttrib(attrtype, attrval);
		}
	}
	else if (mode == 1)
	{
		for (int i = 0; i < buff->def->NumAttr; i++)
		{
			MODATTRIB_TYPE attrtype = buff->def->AttrTypes[i];
			float attrval = buff->def->AttrValues[i];
			addModAttrib(attrtype, -attrval);
		}
	}
}

void LivingAttrib::applyEquips(ActorBody* body, EQUIP_SLOT_TYPE t, bool takeoffAble)
{
	if (body)
	{//npc 怪是没有换装的。没有添加换装逻辑
		body->setTakeoffAble(takeoffAble);
		auto realEquipItem = [this](ActorBody *body, int idx) -> void
		{
			auto itemid = getEquipItem((EQUIP_SLOT_TYPE)idx);
			if (itemid > 0)
			{
				auto tooldef = GetDefManagerProxy()->getToolDef(itemid);
				if (tooldef)
				{
					auto equipType = tooldef->getSlotType();
					if (equipType != EQUIP_NONE)
					{
						int groupType = 0;
						if (equipType == EQUIP_GROUP)
						{
							groupType = 1;
							auto groupDef = GetDefManagerProxy()->getEquipGroupDef(itemid);
							if (groupDef)
							{
								for (auto& item : groupDef->GroupEquipMap)
								{
									EquipPosTypeChangeData t2pData;
									t2pData.setGroupData(0, idx, item.second);
									m_EquipType2Pos[item.first] = t2pData;
								}
								EquipPosTypeChangeData p2tData;
								p2tData.setGroupData(groupType, equipType, itemid);
								m_EquipPos2Type[(EQUIP_BACK_INDEX)idx] = p2tData;
							}
						}
						else
						{
							EquipPosTypeChangeData t2pData;
							t2pData.setGroupData(0, idx, itemid);
							m_EquipType2Pos[equipType] = t2pData;
							EquipPosTypeChangeData p2tData;
							p2tData.setGroupData(groupType, equipType, itemid);
							m_EquipPos2Type[(EQUIP_BACK_INDEX)idx] = p2tData;
						}
					}
				}
			}
			else
			{
				auto equipTypeIter = m_EquipPos2Type.find((EQUIP_BACK_INDEX)idx);
				if (equipTypeIter != m_EquipPos2Type.end())
				{
					auto groupType = equipTypeIter->second.getGroupType();
					if (groupType != 1)
					{
						body->setEquipItem((EQUIP_SLOT_TYPE)equipTypeIter->second.getSourceTypeORIdx(), 0);
						m_EquipType2Pos.erase((EQUIP_SLOT_TYPE)equipTypeIter->second.getSourceTypeORIdx());
					}
					else
					{
						auto equidId = equipTypeIter->second.getSourceItemId();
						const auto def = GetDefManagerProxy()->getEquipGroupDef(equidId);
						if (def)
						{
							for (const auto& item : def->GroupEquipMap)
							{
								body->setEquipItem(item.first, 0);
								auto sourceIdx = m_EquipType2Pos[item.first].getSourceTypeORIdx();
								if (sourceIdx != idx) m_EquipPos2Type.erase((EQUIP_BACK_INDEX)sourceIdx);
								m_EquipType2Pos.erase(item.first);
							}
						}
					}	
					m_EquipPos2Type.erase(equipTypeIter);
				}
			}
		}
		OPTICK_EVENT();
		if (t == MAX_EQUIP_SLOTS)
		{
			for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
			{
				body->setEquipItem((EQUIP_SLOT_TYPE)i, getEquipItem((EQUIP_SLOT_TYPE)i));
			}
		}
		else
		{
			body->setEquipItem(t, getEquipItem(t));
		}
	}
}

float LivingAttrib::getAttackPoint(ATTACK_TYPE atktype, int fromType)
{
	//atktype = (ATTACK_TYPE)getAttackType(atktype);
	if (atktype > MAX_MAGIC_ATTACK && atktype != PHYSICS_ATTACK)
		return 0;

	//float atk = getBasicAttackPoint(atktype);
	float atk = getAttackBaseLua(atktype);

	const int index = AtkType2ArmorIndex(atktype);
	if(index <= MAX_MAGIC_ATTACK + 1)
	{
		BackPackGrid *grid = getEquipGrid(EQUIP_WEAPON);
		if(grid && grid->def)
		{
			//玩家持枪时近战攻击（移动端）不加枪械伤害 by：Jeff
			if (GetDefManagerProxy()->getGunDef(grid->def->ID) && 
				((fromType == ATTACK_RANGE && atktype == ATTACK_RANGE) || !IsKindOf<PlayerAttrib>()))
			{
				const GunDef *gun = GetDefManagerProxy()->getGunDef(grid->def->ID);
				if (gun)
				{
					atk += gun->Attack;
				}
			}
			else if(grid->getDuration()>0)
			{
				const ToolDef *tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
				if (tool && /*tool->AttackType == atktype*/tool->Attacks[index] > 0
					&& tool->AttackType == fromType)
				{
					atk += tool->Attacks[index];
					//连击状态 有攻击力加成
					if (m_OwnerActor)
					{
						ClientPlayer* ownerPlayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
						if (ownerPlayer && ownerPlayer->dataAttackDef && ownerPlayer->ComboAttackStateRunning())
						{
							float atk_bonus = ownerPlayer->dataAttackDef->atk_bonus;
							atk_bonus = atk_bonus <= 0.f ? 1.f : atk_bonus;
							//使连招的最后一击伤害提升：百分比数值 renjie 连招伤害提升附魔
							if ((ownerPlayer->ComboAttackDefNum) == (ownerPlayer->dataAttackDef->index) && checkEnchant(ENCHANT_COMBO_LAST_INC))
							{
								atk_bonus += (float)ownerPlayer->dataAttackDef->atk_rune.atk_rune_bonus;
							}
							atk *= atk_bonus;
						}
					}
				}
			}
			else if (grid->getDuration() == 0)
			{
				if (grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
				{
					const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
					if (tool && /*tool->AttackType == atktype*/tool->Attacks[index] > 0
						&& tool->AttackType == fromType)
					{
						atk += 1;
					}
				}
			}
		}

		if (index <= MAX_PHYSICS_ATTACK)
		{
			//玩法规则设置: 物理攻击点数
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
			{
				if (atktype == fromType)
				{
					PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(this);
					if (playerAttr)
					{
						atk += playerAttr->m_pBaseAttrSetter->m_nAttackPhy;
					}
					/*else
					{
						MobAttrib* mobAttr = dynamic_cast<MobAttrib*>(this);
						if (mobAttr)
						{
							atk += mobAttr->getAttackPhysical();
						}
					}*/
				}

			}
		}
		else
		{
			//玩法规则设置: 元素攻击点数
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
			{
				if (atk > 0)
				{
					PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(this);
					if (playerAttr)
					{
						atk += playerAttr->m_pBaseAttrSetter->m_nAttackMagic;
					}
					/*else
					{
						MobAttrib* mobAttr = dynamic_cast<MobAttrib*>(this);
						if (mobAttr)
						{
							atk += mobAttr->getAttackElem();
						}
					}*/
				}
			}
		}
	}

	return atk;
}

// 注意 armor现在直接代表减伤
float LivingAttrib::getArmorPointByPart(ATTACK_TYPE atktype, ATTACK_BODY_TYPE bodytype)
{
	if(atktype > MAX_MAGIC_ATTACK && atktype != PHYSICS_ATTACK)
		return 0;

	auto it = gBodyType2EquipSlotType.find(bodytype);
	if (it == gBodyType2EquipSlotType.end())
		return 0;

	auto& slotTypes = it->second;

	//float armor = getBasicArmorPoint(atktype);
	float armor = getArmorBaseLua(atktype);

	if(atktype < MAX_PHYSICS_ATTACK)
	{
		armor += getModAttrib((MODATTRIB_TYPE)(MODATTR_ARMOR_PUNCH + atktype));
	}

	const int index = AtkType2ArmorIndex(atktype);
	for(int i=0; i<EQUIP_WEAPON; i++)
	{
		BackPackGrid *grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
		if(grid && grid->def && (grid->getDuration()> 0 || grid->getDuration() == -1))
		{
			bool infinite = grid->getDuration() == -1; //-1无限耐久
			float durable = (float)grid->getDuration();
			const ToolDef *tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if(tool)
			{
				if (slotTypes.find(tool->getSlotType()) == slotTypes.end())
					continue;
				int maxdur = grid->getMaxDuration();
				float equipArmor = 0;

				if (atktype < MAX_PHYSICS_ATTACK)
				{
					if(index >=0 && index < 10)
						equipArmor = tool->Armors[index];		//近战、远程、爆炸
				}
				else
				{
					if (atktype == MAX_MAGIC_ATTACK)
						equipArmor = tool->Armors[MAX_MAGIC_ATTACK + 1];							//魔法防御
					else if (atktype < MAX_MAGIC_ATTACK)
					{
						if (index >= 0 && index < 10)
							equipArmor = tool->Armors[MAX_MAGIC_ATTACK + 1] + tool->Armors[index];	//燃烧、毒素、混乱
					}
				}

				if (maxdur == 0)
					armor += equipArmor;
				else
				{
					// 新伤害计算系统，装备防御力不需要随着耐久降低 code_by:liya designer:huzhe
					if ((GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && durable > 0) || infinite)
					{
						armor += equipArmor;
					}
					else
						armor += equipArmor * durable / maxdur;
				}
			}
		}
	}

	assert(armor >= 0);
	if(armor < 0) armor = 0;
	//if(armor > 20.0f) armor = 20.0f; //modify by null, 去掉防御值的限制.

	//玩法规则设置: 防御点数
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int nAttrDef = 0;
		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(this);

		if (playerAttr)
		{
			if (atktype < MAX_PHYSICS_ATTACK || atktype == PHYSICS_ATTACK)
				nAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefPhy;
			else
				nAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefMagic;
		}		

		armor += nAttrDef;
	}

	return armor;
}

float LivingAttrib::getArmorPoint(ATTACK_TYPE atktype)
{
	if(atktype > MAX_MAGIC_ATTACK && atktype != PHYSICS_ATTACK)
		return 0;

	//float armor = getBasicArmorPoint(atktype);
	float armor = getArmorBaseLua(atktype);

	if(atktype < MAX_PHYSICS_ATTACK)
	{
		armor += getModAttrib((MODATTRIB_TYPE)(MODATTR_ARMOR_PUNCH + atktype));
	}

	const int index = AtkType2ArmorIndex(atktype);
	for(int i=0; i<EQUIP_WEAPON; i++)
	{
		BackPackGrid *grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
		if(grid && grid->def && (grid->getDuration()> 0 || grid->getDuration() == -1))
		{
			bool infinite = grid->getDuration() == -1; //-1无限耐久
			float durable = (float)grid->getDuration();
			const ToolDef *tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if(tool)
			{
				int maxdur = grid->getMaxDuration();
				int equipArmor = 0;

				if (atktype == PHYSICS_ATTACK)
				{
					equipArmor = tool->Armors[MAX_PHYSICS_ATTACK];			//物理防御
				}
				else if (atktype < MAX_PHYSICS_ATTACK)
				{
					if(index >=0 && index < 10)
						equipArmor = tool->Armors[MAX_PHYSICS_ATTACK] + tool->Armors[index];		//近战、远程、爆炸
				}
				else
				{
					if (atktype == MAX_MAGIC_ATTACK)
						equipArmor = tool->Armors[MAX_MAGIC_ATTACK + 1];							//魔法防御
					else if (atktype < MAX_MAGIC_ATTACK)
					{
						if (index >= 0 && index < 10)
							equipArmor = tool->Armors[MAX_MAGIC_ATTACK + 1] + tool->Armors[index];	//燃烧、毒素、混乱
					}
				}

				if (maxdur == 0)
					armor += equipArmor;
				else
				{
					// 新伤害计算系统，装备防御力不需要随着耐久降低 code_by:liya designer:huzhe
					if ((GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && durable > 0) || infinite)
					{
						armor += equipArmor;
					}
					else
						armor += equipArmor * durable / maxdur;
				}
			}
		}
	}

	assert(armor >= 0);
	if(armor < 0) armor = 0;
	//if(armor > 20.0f) armor = 20.0f; //modify by null, 去掉防御值的限制.

	//玩法规则设置: 防御点数
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int nAttrDef = 0;
		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(this);

		if (playerAttr)
		{
			if (atktype < MAX_PHYSICS_ATTACK || atktype == PHYSICS_ATTACK)
				nAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefPhy;
			else
				nAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefMagic;
		}		
		/*else
		{
			MobAttrib* mobAttr = dynamic_cast<MobAttrib*>(this);
			if (mobAttr)
			{
				if (atktype < MAX_PHYSICS_ATTACK || atktype == PHYSICS_ATTACK)
					nAttrDef = mobAttr->getDefPhysical();
				else
					nAttrDef = mobAttr->getDefElem();
			}
		}*/

		armor += nAttrDef;
	}

	return armor;
}

float LivingAttrib::getArmorPointSingle(ATTACK_TYPE atktype)
{
	if (atktype > MAX_MAGIC_ATTACK && atktype != PHYSICS_ATTACK)
		return 0;

	//float armor = getBasicArmorPoint(atktype);
	float armor = getArmorBaseLua(atktype);

	if (atktype < MAX_PHYSICS_ATTACK)
	{
		armor += getModAttrib((MODATTRIB_TYPE)(MODATTR_ARMOR_PUNCH + atktype));
	}

	const int index = AtkType2ArmorIndex(atktype);
	for (int i = 0; i < EQUIP_WEAPON; i++)
	{
		BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
		if (grid && grid->def && (grid->getDuration() > 0 || grid->getDuration() == -1))
		{
			bool infinite = grid->getDuration() == -1; //-1无限耐久
			float durable = (float)grid->getDuration();
			const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
			if (tool)
			{
#if 0
				//old:
				int maxdur = grid->getMaxDuration();
				int offset = AtkType2ArmorIndex(atktype);
				if (maxdur == 0) armor += tool->Armors[offset];
				else armor += tool->Armors[offset] * durable / maxdur;
#else
				//new:TODO
				int maxdur = grid->getMaxDuration();
				int equipArmor = 0;
				
				if (atktype == PHYSICS_ATTACK)
				{
					equipArmor = tool->Armors[MAX_PHYSICS_ATTACK];			//物理防御
				}
				else if (atktype < MAX_PHYSICS_ATTACK)
				{
					if (index >= 0 && index < 10)
						equipArmor = tool->Armors[index];		//近战、远程、爆炸
				}
				else
				{
					if (atktype == MAX_MAGIC_ATTACK)
						equipArmor = tool->Armors[MAX_MAGIC_ATTACK + 1];	//魔法防御
					else if (atktype < MAX_MAGIC_ATTACK)
					{
						if (index >= 0 && index < 10)
							equipArmor = tool->Armors[index];	//燃烧、毒素、混乱
					}
				}

				if (maxdur == 0)
					armor += equipArmor;
				else
				{
					// 新伤害计算系统，装备防御力不需要随着耐久降低 code_by:liya designer:huzhe
					if ((GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && durable > 0) || infinite)
					{
						armor += equipArmor;
					}
					else
						armor += equipArmor * durable / maxdur;
				}
#endif
			}
		}
	}

	assert(armor >= 0);
	if (armor < 0) armor = 0;
	//if(armor > 20.0f) armor = 20.0f; //modify by null, 去掉防御值的限制.

	//玩法规则设置: 防御点数
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		int nPlayerAttrDef = 0;
		PlayerAttrib* playerAttr = dynamic_cast<PlayerAttrib*>(this);

		if (playerAttr)
		{
			if (atktype < MAX_PHYSICS_ATTACK || atktype == PHYSICS_ATTACK)
				nPlayerAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefPhy;
			else
				nPlayerAttrDef = playerAttr->m_pBaseAttrSetter->m_nDefMagic;
		}

		armor += nPlayerAttrDef;
	}

	return armor;
}

float LivingAttrib::antiInjuryEnchant(ATTACK_TYPE atktype)
{
	if(atktype != ATTACK_PUNCH) return 0;

	float damage = 0;
	for(int slot=0; slot<EQUIP_WEAPON; slot++)
	{
		BackPackGrid *grid = getEquipGrid((EQUIP_SLOT_TYPE)slot);
		if(grid == NULL) continue;

		if(grid->getNumEnchant() > 0)
		{
			for(int i=0; i<grid->getNumEnchant(); i++)//有附魔就不可能有符文
			{
				const EnchantDef *def = GetDefManagerProxy()->getEnchantDef(grid->getIthEnchant(i));
				if(def == NULL) continue;
				if(def->EnchantType != ENCHANT_THORNS) continue;

				if(GenRandomInt(100) < def->EnchantValue[0])
				{
					float d = def->EnchantValue[1];
					if(d > grid->getDuration()) 
						d = (float)grid->getDuration();
					if (grid->getDuration() > 0)
					{
						damage += d;
						//需求改变:在现有逻辑上将实际伤害除以8(暂定)用作额外耐久消耗，不足1的以1计算 code by renjie 
						d = (d + 0.5) / 8;
						if (grid->addDuration(-(int)d) <= 0)
						{
							if (!grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
								equip((EQUIP_SLOT_TYPE)slot, nullptr);
						}
					}
				}
			}
		}else{
			const GridRuneData& rune = grid->getRuneData();
			if(rune.getRuneNum() > 0){
				for(int i=0; i<rune.getRuneNum(); i++)//符文
				{
					const RuneDef *def = rune.findDefByIndex(i);
					if(def == NULL) continue;
					if(def->EnchantType != ENCHANT_THORNS) continue;

					if(GenRandomInt(100) < rune.getItemByIndex(i).getRuneVal0())
					{
						float d = rune.getItemByIndex(i).getRuneVal1(); //def->Value[1];
						if(d > grid->getDuration()) d = (float)grid->getDuration();
						if (grid->getDuration() > 0)
						{
							damage += d;
							//需求改变:在现有逻辑上将实际伤害除以8(暂定)用作额外耐久消耗，不足1的以1计算 code by renjie 
							d = (d + 0.5) / 8;
							if (grid->addDuration(-(int)d) <= 0)
							{
								if (!grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
									equip((EQUIP_SLOT_TYPE)slot, nullptr);
							}
						}
					}
				}
			}
		}
	}

	return damage;
}

int LivingAttrib::getDigProbEnchant()
{
	return (int)getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_DIG_PROB, ATTACK_ALL, ATTACK_TARGET_ALL);
}

float LivingAttrib::getEnchantAttackPoint(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype)
{
	return getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_SHARP, atktype, targettype);
}

float LivingAttrib::getEnchantAttackPercent(ATTACK_TYPE atktype)
{
	return getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_BOW_ATTACK, ATTACK_ALL, ATTACK_TARGET_ALL);
}

float LivingAttrib::getEnchantArmorPoint(ATTACK_TYPE atktype, bool hasAllArmor)
{
	float armor = 0;
	for(int i=0; i<EQUIP_WEAPON; i++)
	{
		armor += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, ENCHANT_PROTECTION, atktype, ATTACK_TARGET_ALL, nullptr, hasAllArmor);
	}
	return armor;
}

float LivingAttrib::getEnchantDamageDecPer(ENCHANT_TYPE enchanttype, ATTACK_TYPE atktype, bool hasAllArmor)//所有伤害减免 renjie
{
	float Dec = 0;
	for (int i = 0; i < EQUIP_WEAPON; i++)
	{
		Dec += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, enchanttype, atktype, ATTACK_TARGET_ALL, nullptr, hasAllArmor);
	}
	return Dec;
}

float LivingAttrib::getEnchantIncPer(ENCHANT_TYPE enchanttype, ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype, bool hasAllArmor)//获取所有装备的特定附魔IncVal renjie
{
	float Inc = 0;
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		Inc += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, enchanttype, atktype, targettype, nullptr, hasAllArmor);
	}
	return Inc;
}

float LivingAttrib::getEnchantJumpHeightInc() // 增加跳跃能力：数值 renjie
{
	float Inc = 0;
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		Inc += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, ENCHANT_JUMP_INC, ATTACK_ALL, ATTACK_TARGET_ALL, nullptr, true);
	}
	return Inc;
}

float LivingAttrib::getEnchantExpInc() // - 星星经验获取加成：百分比数值 renjie
{
	float Inc = 0;
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		Inc += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, ENCHANT_STAR_INC, ATTACK_ALL, ATTACK_TARGET_ALL, nullptr, true);
	}
	return Inc;
}

float LivingAttrib::getEnchantWeponSkillDec() // - - 武器技能冷却时间降低：百分比数值，降低使用的间隔时间 renjie
{
	float Dec = 0;

	Dec = getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_WEAPON_SKILL_CD_DEC, ATTACK_ALL, ATTACK_TARGET_ALL, nullptr, true);
	
	return Dec;
}

bool LivingAttrib::checkEnchant(ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype) // - 检查是否有某个附魔 renjie
{
	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		if (getEquipEnchant((EQUIP_SLOT_TYPE)i, enchanttype, attacktype, targettype) || getEquipRuneEnchant((EQUIP_SLOT_TYPE)i, enchanttype, attacktype, targettype))//老符文 || 新符文	
		{	
			return true;
		}
	}
	return false;
}

const RuneDef *LivingAttrib::getEquipRuneEnchant(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype)//获取新符文
{
	BackPackGrid* grid = getEquipGrid(slot);
	if (grid == NULL || grid->def == NULL || grid->getDuration() <= 0) return 0;

	GridRuneData& runedata = grid->getRuneData();

	if (runedata.getRuneNum() > 0)
	{
		for (int i = 0; i < runedata.getRuneNum(); i++)
		{
			const RuneDef* def = runedata.findDefByIndex(i);
			if (def == NULL) continue;
			if (def->EnchantType != enchanttype) continue;
			if (def->AttackType != ATTACK_ALL && def->AttackType != attacktype) continue;
			if (def->TargetType != ATTACK_TARGET_ALL && def->TargetType != targettype) continue;
			return def;
		}
	}
	return NULL;
}


float LivingAttrib::getEquipEnchantValueOld(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype, float *value2, bool hasAllArmor)
{
	BackPackGrid *grid = getEquipGridWithType(slot);
	if(value2) *value2 = 0;
	if(grid == NULL || grid->def == NULL || grid->getDuration() <= 0) return 0;

	for(int i=0; i<grid->getNumEnchant(); i++)
	{
		const EnchantDef *def = GetDefManagerProxy()->getEnchantDef(grid->getIthEnchant(i));
		if(def == NULL) continue;
		if(def->EnchantType != enchanttype) continue;
		if((!hasAllArmor || def->AttackType!=ATTACK_ALL) && def->AttackType != attacktype) continue;
		if(def->TargetType!=ATTACK_TARGET_ALL && def->TargetType!=targettype) continue;

		if(value2) *value2 = def->EnchantValue[1];
		return def->EnchantValue[0];
	}

	return 0;

}

float LivingAttrib::getEquipRuneValue(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE  attacktype, ATTACK_TARGET_TYPE targettype, float *value2, bool hasAllArmor)
{
	BackPackGrid *grid = getEquipGridWithType(slot);
	if(value2) *value2 = 0;
	if(grid == NULL || grid->def == NULL || grid->getDuration() <= 0) return 0;

	GridRuneData& runedata = grid->getRuneData();
	if (runedata.getRuneNum() > 0)
	{
		for(int i=0; i < runedata.getRuneNum(); i++)
		{
			const RuneDef *def = runedata.findDefByIndex(i);
			if(def == NULL) continue;
			if(def->EnchantType != enchanttype) continue;
			if((!hasAllArmor || def->AttackType != ATTACK_ALL) && def->AttackType != attacktype) continue;
			if(def->TargetType!=ATTACK_TARGET_ALL && def->TargetType!=targettype) continue;

			if(value2) *value2 = runedata.getItemByIndex(i).getRuneVal1(); //def->Value[1];
			return runedata.getItemByIndex(i).getRuneVal0();
			//return def->Value[0];
		}
	}
	return 0;
}

float LivingAttrib::getEquipEnchantValue(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype, float *value2, bool hasAllArmor)
{
	//同一件装备上 不可能符文跟附魔共存
	float ret = getEquipEnchantValueOld(slot, enchanttype, attacktype, targettype, value2, hasAllArmor);
	if(ret > 0)
		return ret;
	////////
	return getEquipRuneValue(slot, enchanttype, attacktype, targettype, value2, hasAllArmor);
	//return 
}

const EnchantDef *LivingAttrib::getEquipEnchant(EQUIP_SLOT_TYPE slot, ENCHANT_TYPE enchanttype, ATTACK_TYPE attacktype, ATTACK_TARGET_TYPE targettype)
{
	BackPackGrid *grid = getEquipGridWithType(slot);
	if(grid == NULL || grid->def == NULL) return NULL;

	for(int i=0; i<grid->getNumEnchant(); i++)
	{
		const EnchantDef *def = GetDefManagerProxy()->getEnchantDef(grid->getIthEnchant(i));
		if(def == NULL) continue;
		if(def->EnchantType != enchanttype) continue;
		if(def->AttackType!=ATTACK_ALL && def->AttackType!=attacktype) continue;
		if(def->TargetType!=ATTACK_TARGET_ALL && def->TargetType!=targettype) continue;

		return def;
	}

	return NULL;
}

int LivingAttrib::getEquipMineBlockEfficiency(int baseEfficiency)
{
	if (baseEfficiency <= 0)
		return baseEfficiency;
	BackPackGrid* grid = getEquipGrid(EQUIP_WEAPON);
	if (grid == NULL || grid->def == NULL ) 
		return baseEfficiency;
	if (grid->getDuration() <= 0)
	{
		if (grid->haveEnchant(ENCHANT_DURABLE_PROTECT))
			return 10;
	}
	return baseEfficiency;

}

void LivingAttrib::addModAttrib(int attrtype, float v)
{
	//assert(attrtype>=0 && attrtype<m_Attribs.size());
	if(attrtype >= (int)(m_Attribs.size()))
	{
		return;
	}
	m_Attribs[attrtype].value += v;

	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_MOD_ATTRIB, attrtype, m_OwnerActor->getObjId()&0xffffffff, (char*)&v, sizeof(v));
}

float LivingAttrib::getModAttrib(int attrtype)
{
	//assert(attrtype>=0 && attrtype<(int)(m_Attribs.size()));
	bool isOK = attrtype >= 0 && attrtype < (int)(m_Attribs.size());
	//assert(isOK);
	if (!isOK)
	{
		return 0.0f;
	}
	return m_Attribs[attrtype].value;
}

bool LivingAttrib::setModAttrib(int attrtype, float v)
{
	if (attrtype >= (int)(m_Attribs.size()))
	{
		return false;
	}
	m_Attribs[attrtype].value = v;

	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_MOD_ATTRIB, attrtype, m_OwnerActor->getObjId() & 0xffffffff, (char*)&v, sizeof(v));
	return true;
}
bool LivingAttrib::addRune(int slot, const GridRuneItemData &one)
{
	assert(slot>=0 && slot<MAX_EQUIP_SLOTS);
	BackPackGrid *equip = getEquipGridWithType((EQUIP_SLOT_TYPE)slot);
	if(equip == NULL) return false;
	return equip->addRune(one);
}

bool LivingAttrib::removeRune(int slot, int runeid)
{
	assert(slot>=0 && slot<MAX_EQUIP_SLOTS);
	BackPackGrid *equip = getEquipGridWithType((EQUIP_SLOT_TYPE)slot);
	if(equip == NULL) return false;
	return equip->removeRune(runeid);
}
void  LivingAttrib::setFirstSearch(bool firstSearch)
{
	m_FirstSearch = firstSearch;
}

bool LivingAttrib::getFirstSearch()
{
	return m_FirstSearch;
}

bool LivingAttrib::addEnchant(int slot, int enchantId, int enchantLevel)
{
	assert(slot>=0 && slot<MAX_EQUIP_SLOTS);

	BackPackGrid *equip = getEquipGridWithType((EQUIP_SLOT_TYPE)slot);
	if(equip == NULL) return false;

	return equip->addEnchant(enchantId*100+enchantLevel);
}

bool LivingAttrib::removeEnchant(int slot, int enchantId)
{
	assert(slot>=0 && slot<MAX_EQUIP_SLOTS);

	BackPackGrid *equip = getEquipGridWithType((EQUIP_SLOT_TYPE)slot);
	if (equip == NULL) return false;
	return equip->removeEnchant(enchantId*100);
}

void LivingAttrib::attackedByBuff(int atktype, float atkpoints, long long fromObjid/* = 0*/, int buffid/* = 0*/, int bufflv/* = 0*/)
{
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((atktype >= ATTACK_PUNCH && atktype <= MAX_MAGIC_ATTACK) || atktype == PHYSICS_ATTACK))
	{
		const int index = AtkType2ArmorIndex((ATTACK_TYPE)atktype);
		atkdata.atkTypeNew = (1 << index);
		if (atktype == ATTACK_EXPLODE)
		{
			atkdata.explodePoints[0] = atkpoints;
		}
		else
		{
			atkdata.atkPointsNew[index] = atkpoints;
		}
	}
	else
	{
		atkdata.atktype = (ATTACK_TYPE)atktype;
		atkdata.atkpoints = atkpoints;
	}
	
	if (!hasBuff(buffid, bufflv))//避免循环buff
	{
		atkdata.buffId = buffid;
		atkdata.buffLevel = bufflv;
	}
	
	//设置buff伤害来源
	if (fromObjid > 0)
	{
		if (m_OwnerActor->getActorMgr())
		{
			ClientActor* pActor = m_OwnerActor->getActorMgr()->findActorByWID(fromObjid);
			if (pActor)
			{
				m_OwnerActor->setBeHurtTarget(pActor);
				m_OwnerActor->setBeAtk(pActor);
			}
		}
	}

	//受到BUF伤害白天躺床上受到伤害起来
	if (dynamic_cast<PlayerControl*>(m_OwnerActor))
	{
		PlayerControl* pPlayerControl = dynamic_cast<PlayerControl*>(m_OwnerActor);
		World* pWorld = pPlayerControl->getWorld();
		if (pWorld && pWorld->isDaytime() && pPlayerControl->isRestInBed())
		{
			pPlayerControl->dismountActor();
		}
	}
	m_lastAttackBuffLv = bufflv;
	m_lastAttackBuffId = buffid;

	auto player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (player) 
	{
		//atkdata 为了避免循环buff buffid不会记录这里手动构造一个
		OneAttackData o_atkdata = atkdata;
		o_atkdata.buffId = buffid;
		o_atkdata.buffLevel = bufflv;
		player->updataLastAttackDataInfo(o_atkdata, nullptr);
	}

	attackedFrom(atkdata);
}

bool LivingAttrib::hasNeedSwitchTPSBuff()
{
	for (size_t i = 0; i < m_Buffs.size(); i++)
	{
		const BuffDef *def = GetDefManagerProxy()->getStatusDef(GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv));
		if (def && def->NeedSwitchTPS) return true;
	}
	return false;
}

float LivingAttrib::getArmorPointLua(int atktype)
{
	return getArmorPoint((ATTACK_TYPE)atktype);
}

bool LivingAttrib::hasHarmfulBuff()
{
	for (size_t i = 0; i < m_Buffs.size(); i++)
	{
		const BuffDef* def = GetDefManagerProxy()->getStatusDef(GetDefManagerProxy()->getRealStatusId(m_Buffs[i].buffid, m_Buffs[i].bufflv));
		if (def && def->TriggerType == 1)
		{
			return true;
		}
	}
	return false;
}

void LivingAttrib::damageRemoveBuff(float hp, int atktype, int attackTargetType)
{
	for (size_t i = 0; i < m_Buffs.size(); i++)
	{
		const BuffDef* def = m_Buffs[i].def;
		if (def)
		{
			for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {

				if (def->Status.EffInfo[j].CopyID != 0) {
					auto effDef = GetDefManagerProxy()->getBuffEffectDef(def->Status.EffInfo[j].CopyID);
					if (effDef && effDef->AttType == BUFFATTRT_DAMAGE_REMOVE_BUFF)
					{
						int defAtkid = def->Status.EffInfo[j].Value[0];
						auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef(defAtkid);
						int defAtkType = enumdef->AttType;
						bool isSameType = false;
						if (defAtkType == 1030)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_MONSTER_HURT && attackTargetType >= ATTACK_TARGET_UNDEAD && attackTargetType <= ATTACK_TARGET_ANCIENT)
						{
							isSameType = true;
						}
						else if(defAtkType == BUFFATTRT_ACTOR_HURT && attackTargetType == ATTACK_TARGET_ANIMAL)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_PLAYER_HURT && attackTargetType == ATTACK_TARGET_PLAYER)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_SAVAGE_HURT && attackTargetType == ATTACK_TARGET_SAVAGE)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_FALL_HURT && atktype == ATTACK_FALLING)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_ICE_TEMPERATURE && atktype == ATTACK_ICE)
						{
							isSameType = true;
						}
						else if (defAtkType >= BUFFATTRT_MELEE_HURT && defAtkType <= BUFFATTRT_CONFUSION_HURT && atktype + BUFFATTRT_MELEE_HURT == defAtkType)
						{
							isSameType = true;
						}
						else if (defAtkType == BUFFATTRT_ZOMBIE_HURT && attackTargetType == ATTACK_TARGET_ZOMBIE)
						{
							isSameType = true;
						}


						if (isSameType)
						{
							m_Buffs[i].damageHPToRemoveBuff -= hp;
							float value = GetSliderValue(effDef->EffectParam[1].UIType, (float)(def->Status.EffInfo[j].Value[1]));
							if (value != SLIDER_VALUE_ERROR && value < m_Buffs[i].damageHPToRemoveBuff)
							{
								m_RemoveBuffIds.push_back(m_Buffs[i].buffid);
							}
							break;
						}
					}
				}
				else {
					break;
				}
			}
		}
	}
}

float LivingAttrib::getAttackBaseLua(int attacktype)
{
	if (attacktype == ATTACK_TYPE::ATTACK_PUNCH || attacktype == ATTACK_TYPE::ATTACK_RANGE) {
		float fBaseAttack = m_fBaseAttack[attacktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
		return (fBaseAttack < 0.0f ? getBasicAttackPoint((ATTACK_TYPE)attacktype) : fBaseAttack);
	}

	return getBasicAttackPoint((ATTACK_TYPE)attacktype);
}

void LivingAttrib::setAttackBaseLua(int attacktype, float v)
{
	if (attacktype == ATTACK_TYPE::ATTACK_PUNCH || attacktype == ATTACK_TYPE::ATTACK_RANGE) {
		m_fBaseAttack[attacktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1] = v;
		if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isRemote())//主机才需要发送属性同步消息
			m_OwnerActor->syncAttr(attacktype == ATTACK_TYPE::ATTACK_PUNCH ? ATTRT_ATK_MELEE : ATTRT_ATK_REMOTE, v);//近战或者远程攻击
		else if (m_OwnerActor == g_pPlayerCtrl)//客机需要触发属性改变事件(主机不)
		{
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
	}
}

float LivingAttrib::getArmorBaseLua(int attacktype)
{
	if (attacktype == ATTACK_TYPE::ATTACK_PUNCH || attacktype == ATTACK_TYPE::ATTACK_RANGE) {
		float fBaseArmor = m_fBaseArmor[attacktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
		return (fBaseArmor < 0.0f ? getBasicArmorPoint((ATTACK_TYPE)attacktype) : fBaseArmor);
	}
	
	return getBasicArmorPoint((ATTACK_TYPE)attacktype);
}

void LivingAttrib::setArmorBaseLua(int attacktype, float v)
{
	if (attacktype == ATTACK_TYPE::ATTACK_PUNCH || attacktype == ATTACK_TYPE::ATTACK_RANGE) {
		m_fBaseArmor[attacktype == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1] = v;
		World* pworld = m_OwnerActor->getWorld();
		if (pworld && !pworld->isRemoteMode())//主机才需要发送属性同步消息
			m_OwnerActor->syncAttr(attacktype == ATTACK_TYPE::ATTACK_PUNCH ? ATTRT_DEF_MELEE : ATTRT_DEF_REMOTE, v);//近战或者远程防御
		else if (m_OwnerActor == g_pPlayerCtrl)//客机需要触发属性改变事件(主机不)
		{
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
	}
}

#define AttackTypeNameMAX 20
static const char* AttackTypeName[AttackTypeNameMAX] = {
	"PUNCH",
	"RANGE",
	"EXPLODE",
	"FIRE",
	"POISON",
	"WITHER",
	"FLASH",
	"SUN",
	"FALLING",
	"ANVIL",
	"CACTUS",
	"WALL",
	"DROWN",
	"SUFFOCATE",
	"ANTIINJURY",
	"BLOCK_LASER",
	"FIXED",
	"PHYSICS_ATTACK",
	"TRUE_DAMAGE",
	"ATTACK_THORNBALL"
};

void LivingAttrib::AttackedByScorpionSpineShield(OneAttackData &atkdata)
{
    if (atkdata.fromplayer != nullptr) {
        int curToolId = atkdata.fromplayer->getCurToolID();
        // 蝎刺盾逻辑，触发附加buff后，根据buff等级进行变动升级buff等级
        // 玩家buff叠加到5级，变为剧毒buff，且剧毒buff状态下不进行叠加逻辑
        if (curToolId == 12310) {
            int random = GenRandomInt(1, 2);
            if (random > 1) {
                bool bFindBuff = false;
                bool bRemoveBuff = false;
                for (size_t i = 0; i < m_Buffs.size(); i++) {
                    if (m_Buffs[i].buffid == SCORPION_VENUM_BASE_BUFF) {
                        atkdata.buffId = VIRULENT_BUFF;
                        atkdata.buffLevel = 1;
                        bFindBuff = true;
                        bRemoveBuff = true;
                        break;
                    } else if (m_Buffs[i].buffid == VIRULENT_BUFF &&
                        m_Buffs[i].bufflv == 1) {
                        atkdata.buffId = VIRULENT_BUFF;
                        atkdata.buffLevel = 1;
                        bFindBuff = true;
                        break;
                    }
                }
                // 因为剧毒buff等级只有1级，所以升级成剧毒buff，需要先删除蝎毒的buff
                if (bRemoveBuff) {
                    removeBuff(SCORPION_VENUM_BASE_BUFF);
                }
                if (!bFindBuff) {
                    atkdata.buffId = SCORPION_VENUM_BASE_BUFF;
                    atkdata.buffLevel = 5;
                }
            }
        }
    }
}

void LivingAttrib::addEquipEntryBuff(int iEquipId, int armType)
{
	const BuffDef* pDef = GetDefManagerProxy()->getStatusDef(1001000);//先取空白模版的配置
	if (!pDef) { return; }

	std::vector<ModEntryCreate> entrys;
	if (armType == 0)
	{
		BackPackGrid* wgrid = getEquipGridWithType(EQUIP_WEAPON);
		if (!wgrid) return;
		GunGridDataComponent* gunGrideDate = dynamic_cast<GunGridDataComponent*>(wgrid->getGunDataComponent());
		if (!gunGrideDate) return;
		gunGrideDate->GetModEntrys(entrys);
	}
	else if (armType == 1)
	{
		const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(iEquipId);
		if (!toolDef) return;
		auto slotType = toolDef->getSlotType();
		BackPackGrid* grid =nullptr;
		if (slotType != EQUIP_NONE)
		{
			grid = getEquipGridWithType(slotType);
		}
		if (!grid) return;
		EntryGridDataComponent* entryGridDataComponent = dynamic_cast<EntryGridDataComponent*>(grid->getEntryDataComponent());
		if (!entryGridDataComponent) return;
		entrys = entryGridDataComponent->GetModEntryCreateVector();
	}
	if (entrys.empty()) return;
	//开始加效果
	EquipEntryStatus status;
	status.equipID = iEquipId;
	for (auto& entry : entrys)
	{
		if (entry.effectBanks.empty())
			continue;

		BuffDef stStatusDef = *pDef;
		stStatusDef.CopyID = 1001000;
		stStatusDef.ID = iEquipId;
		stStatusDef.BuffType = 2;
		stStatusDef.Status.LimitTime = 0;
		stStatusDef.Status.Priority = 2;
		stStatusDef.Status.DeathClear = 1;
		stStatusDef.Status.AttackClear = 2;
		stStatusDef.Status.DamageClear = 2;
		for (int i = 0; i < entry.effectBanks.size(); i++)
		{
			if (i >= MAX_EQUIP_EFFECT_COUNT)
				break;

			ModEntryCreateEffect& modEntryCreateEffect = entry.effectBanks[i];
			EffectInfo effectInfo;
			effectInfo.CopyID = modEntryCreateEffect.effectBankID;
			memcpy(effectInfo.Value, modEntryCreateEffect.Value, sizeof(double) * MAX_BUFF_ATTRIBS);
			stStatusDef.Status.EffInfo[i] = effectInfo;
		}
		m_EquipBuffDefs.emplace_back(stStatusDef);
		ActorBuff stStatus;
		stStatus.buffid = iEquipId;
		stStatus.bufflv = iEquipId;
		stStatus.fromObjid = 0;
		stStatus.buffidx = 0;
		stStatus.ticks = 0;
		stStatus.def = (const BuffDef*)(&m_EquipBuffDefs.back());
		status.entryBuffs.push_back(stStatus);
		addStatusEffects(&status.entryBuffs.back());
	}
	m_EquipBuffs.push_back(status);
}

void LivingAttrib::removeEquipEntryBuff(int iEquipId)
{
	for (auto iter = m_EquipBuffDefs.begin(); iter != m_EquipBuffDefs.end();)
	{
		BuffDef& buffDef = *iter;
		if (buffDef.ID == iEquipId)
		{
			ActorBuff stStatus;
			stStatus.buffid = iEquipId;
			stStatus.bufflv = iEquipId;
			stStatus.fromObjid = 0;
			stStatus.buffidx = 0;

			stStatus.ticks = 0;
			stStatus.def = &buffDef;
			removeStatusEffects(&stStatus);
			iter = m_EquipBuffDefs.erase(iter);
		}
		else
		{
			iter++;
		}
	}

	for (auto iter = m_EquipBuffs.begin(); iter != m_EquipBuffs.end();)
	{
		auto& buffDef = *iter;
		if (buffDef.equipID == iEquipId)
		{
			iter = m_EquipBuffs.erase(iter);
		}
		else
		{
			iter++;
		}
	}
}

bool LivingAttrib::attackedFrom(OneAttackData &atkdata, ClientActor *attacker /* = NULL */)
{
 	if (!m_OwnerActor)
		return false;
	if(m_OwnerActor->isDead())
	{
		//LOG_INFO("LivingAttrib::attackedFrom(): false 1");
		return false;
	}

	// 不管是否收到伤害，都做效果判断（给攻击者概率加状态） 策划：cjt
	randomAddStateToAttacker(attacker);
	randomAddStateToAttack(attacker);

	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (!(ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_BEATTACKED)))
	{
		//LOG_INFO("LivingAttrib::attackedFrom(): false 2");
		return false;
	}

	bool newAttackExplode = false;
	bool isOpenNewHpdecCalculate = GetLuaInterfaceProxy().get_lua_const() && GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate;
	// 伤害免疫
	if (isOpenNewHpdecCalculate && atkdata.atkTypeNew > 0)
	{
		// 爆炸攻击
		int flag = (1 << ATTACK_EXPLODE);
		if ((atkdata.atkTypeNew & flag) > 0)
		{
			newAttackExplode = true;
			if (hasImmuneAttackType(getImmuneTypeByAttackType(ATTACK_EXPLODE)))
			{
				return false;
			}
		}
		else
		{
			float totalAtkPoints = 0.f;
			bool hasImmune = false;
			for (int i = 0; i < 10; i++)
			{
				if (atkdata.atkPointsNew[i] > 0)
				{
					ATTACK_TYPE atkType = ArmorIndex2AtkType(i);
					if (hasImmuneAttackType(getImmuneTypeByAttackType(atkType)))
					{
						atkdata.atkPointsNew[i] = 0;
						hasImmune = true;
					}
				}
				totalAtkPoints += atkdata.atkPointsNew[i];
			}
			if (hasImmune && totalAtkPoints <= std::numeric_limits<float>::epsilon())
			{
				return false;
			}
		}
	}
	else
	{
		int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atktype) : atkdata.atktype);
		if (hasImmuneAttackType(getImmuneTypeByAttackType(iAttackType)))
		{
			return false;
		}
	}

	if(atkdata.atktype==ATTACK_SUN)
	{
		bool hurt = /*true*/damageEquipItemWithType(EQUIP_HEAD, GenRandomInt(0, 1)) == 0;
		//int helmet = getEquipItem(EQUIP_HEAD);
		//if(helmet > 0)
		//{
		//	damageEquipItemWithOutPos(EQUIP_HEAD, GenRandomInt(0,1));
		//	hurt = false;
		//}

		if (hurt)
		{
			auto FireBurnComp = m_OwnerActor->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(100, 1);
			}

		}
		return true;
	}

	if (m_OwnerActor->isInvulnerable(attacker))
	{
		return false;
	}
	/*
	if((atkdata.atktype==ATTACK_FALLING||atkdata.atktype==ATTACK_ANVIL) && getEquipItemWithType(EQUIP_HEAD)!=0)
	{
		int itemdamage = int(atkdata.atkpoints*4.0f + GenRandomFloat()*atkdata.atkpoints*2.0f);
		damageEquipItemWithOutPos(EQUIP_HEAD, itemdamage);
		atkdata.atkpoints *= 0.75f;
	}*/

	atkdata.knockback -= getKnockbackResistance();
	if(atkdata.knockback < 0) 
		atkdata.knockback = 0;
	else if (GenRandomFloat() < getModAttrib(MODATTR_KNOCK_RESIST_PROB)) 
		atkdata.knockback = 0;
	//else if (GenRandomFloat() < 0.5f) 
	//	atkdata.knockback = 0;
	
	//LOG_INFO("LivingAttrib::attackedFrom(): %3d | %3.1f | %3.1f", atkdata.ignore_resist ? 1 : 0, atkdata.atkpoints, m_MaxHurtInResistant);
	if(m_HurtResistantTime > MAX_HURTRESISTANT_TIME/2)
	{
		float totalAtkPoints = 0.f;
		if (isOpenNewHpdecCalculate && atkdata.atkTypeNew > 0)
		{
			for (int i = 0; i < 10; i++)
			{
				totalAtkPoints += atkdata.atkPointsNew[i];
			}
			for (int i = 0; i < 7; i++)
			{
				totalAtkPoints += atkdata.explodePoints[i];
			}
		}
		else
		{
			totalAtkPoints = atkdata.atkpoints;
		}
		if (g_WorldMgr->IsAttackedProtect())
		{
			if (!atkdata.ignore_resist && totalAtkPoints <= m_MaxHurtInResistant)
			{
				//LOG_INFO("LivingAttrib::attackedFrom(): false 3");
				return false;
			}
		}

		if (atkdata.atktype != ATTACK_FLASH)	// 闪电链是跟随普攻的，在保护期内不刷新这个阈值，不然会导致一直重置普攻
		{
			//m_MaxHurtInResistant = atkdata.atkpoints;
			m_MaxHurtInResistant = totalAtkPoints;
		}
		atkdata.knockback = 0;
	}
	else
	{
		if (isOpenNewHpdecCalculate && atkdata.atkTypeNew > 0)
		{
			m_MaxHurtInResistant = 0;
			for (int i = 0; i < 10; i++)
			{
				m_MaxHurtInResistant += atkdata.atkPointsNew[i];
			}
			for (int i = 0; i < 7; i++)
			{
				m_MaxHurtInResistant += atkdata.explodePoints[i];
			}
		}
		else
		{
			m_MaxHurtInResistant = atkdata.atkpoints;
		}
		m_HurtResistantTime = MAX_HURTRESISTANT_TIME;
		if (m_OwnerActor && m_OwnerActor->getBody())
		{
			auto effectComponent = m_OwnerActor->getEffectComponent();
			if (effectComponent)
			{
				// 关闭击退处理
				//int opId = 0;
				//float val = 0.0f;
				//if (g_WorldMgr && g_WorldMgr->isGameMakerRunMode())
				//{
				//	g_WorldMgr->getRuleOptionID(GMRULE_KNOCKBACK, opId, val);
				//}

				//ActorLiving* actorliving = dynamic_cast<ActorLiving*>(m_OwnerActor);
				//if (opId != 2 && (!actorliving || !actorliving->IsInDefanceState()))
				//{
				//	if (atkdata.atktype == ATTACK_EXPLODE || newAttackExplode)
				//	{
				//		effectComponent->playBodyEffect(BODYFX_EXPLODE_HURT);
				//	}
				//	else
				//	{
				//		effectComponent->playBodyEffect(BODYFX_HURT);
				//	}
				//}
			}
		}
	}

    //AttackedByScorpionSpineShield(atkdata);

	float hpdec = 0.f;
	// 物理、元素、爆炸使用新伤害系统
 	if (isOpenNewHpdecCalculate && atkdata.atkTypeNew > 0)
	{
		hpdec = calculateHpdecNew(atkdata, attacker);
	}
	else
	{
		hpdec = calculateHpdecOld(atkdata, attacker);
	}
	// 击退抗性
	handleRepelResist(atkdata.knockback);

	// 韧性处理，放到防御处理前，是因为防御处理中可能会退出防御状态
	handleToughnessDeduction(attacker, atkdata);

	// 防御系统 - 防御状态下减少受到伤害
	bool successDefance = handleDefanceStateDamaged(hpdec, attacker, atkdata);

	if (hpdec < 0.0f)
	{ 
		hpdec = 0.0f; 
	}

	if (isOpenNewHpdecCalculate && atkdata.atkTypeNew > 0)
	{
		if (hpdec > 0)
		{
			//int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atkTypeNew) : atkdata.atkTypeNew);
			//if (!hasImmuneType(getImmuneTypeByAttackType(iAttackType)) || iAttackType != atkdata.atkTypeNew)
			{
				addHP(-hpdec);

				int attackTargetType = attacker ? attacker->getAttackTargetType() : ATTACK_TARGET_OTHERS;
				damageRemoveBuff(-hpdec, atkdata.atktype, attackTargetType);
			}
		}
	}
	else
	{
		int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atktype) : atkdata.atktype);
		if (!hasImmuneType(getImmuneTypeByAttackType(iAttackType)))
		{
			if (iAttackType == TRUE_DAMAGE)
			{
				addHPByTrueDamage(-hpdec);
			}
			else
			{
				addHP(-hpdec);
			}
			int attackTargetType = attacker ? attacker->getAttackTargetType() : ATTACK_TARGET_OTHERS;
			damageRemoveBuff(-hpdec, atkdata.atktype, attackTargetType);
		}
	}
		
	if(atkdata.damage_armor) 
		damageArmor(1,attacker);

	if(ActorAttrib::m_DisplayHurtNumber)
	{
		char msgbuf[256];
		const char *who = "I";
		float food = 0;
		MobAttrib *mobattr = dynamic_cast<MobAttrib *>(this);
		if(mobattr)
		{
			who = mobattr->getDef()->Name.c_str();
			food = mobattr->getFood();
		}
		if (atkdata.atktype >= 0 && atkdata.atktype < AttackTypeNameMAX)
		{
			const char* atkname = AttackTypeName[atkdata.atktype];
			sprintf(msgbuf, "&%s hurt: HP=%.2f/%.2f, atktype=%s, atkpoints=%.2f, knock=%.2f, Hunger=%.2f", who, hpdec, m_Life, atkname, atkdata.atkpoints, atkdata.knockback, food);
		}

		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(msgbuf);
	}

	// 防御成功不添加buff
	if (atkdata.buffId > 0 && !successDefance)
	{
		long long fromObjid = 0;
		if (atkdata.fromplayer)
			fromObjid = dynamic_cast<ClientPlayer*>(atkdata.fromplayer)->getObjId();
		
		addBuff(atkdata.buffId, atkdata.buffLevel, -1, 0, fromObjid);
	}
	else
	{
		m_lastAttackBuffId = 0;
		m_lastAttackBuffLv = 0;
	}

	if (hpdec > 0 && attacker)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(attacker);
		if (player)
		{
			player->damageActorToTrigger(m_OwnerActor->getObjId(), m_OwnerActor->getDefID(), (int)(hpdec), atkdata.isAttackHead, atkdata.ignoreTriggerEvent);
		}
		else {
			if (m_OwnerActor)
			{
				auto triggerComponent = m_OwnerActor->getTriggerComponent();
				if (triggerComponent)
				{
					triggerComponent->ActorDamageOnTrigger(attacker, (int)(-hpdec), atkdata.atktype, atkdata.triggerhit, atkdata.isAttackHead);
				}
			}
		}

		ActorLiving* pFromActor = NULL;
		int iObjType = attacker->getObjType();
		if (iObjType == OBJ_TYPE_THROWABLE || iObjType == OBJ_TYPE_ARROW) {
			pFromActor = dynamic_cast<ActorLiving *>(attacker->getShootingActor());
		}
		else {
			pFromActor = dynamic_cast<ActorLiving *>(attacker);
		}

		if (pFromActor && pFromActor->getLivingAttrib()) {
			std::vector<StatusAttInfo> vValue;
			pFromActor->getLivingAttrib()->getStatusAddAttInfo(BuffAttrType::BUFFATTRT_ATTACK_ADD, vValue);

			for (int i = 0; i < (int)vValue.size(); i++) {
				if (vValue[i].vValue.size() > 2) {
					if (vValue[i].vValue[0].iType == 1) {
						int iRandNum = 1 + GenRandomInt(100);
						if (iRandNum <= vValue[i].vValue[0].value) {//命中
							int ticks = (int)(vValue[i].vValue[1].value * 20);
							if (vValue[i].vValue[1].iType == 3) { ticks /= 10; }

							int buffid = (int)vValue[i].vValue[2].value;
							int bufflv = 1;
							if (!GetDefManagerProxy()->isCustomStatus(buffid)) {
								bufflv = buffid % 1000;
								buffid = buffid / 1000;
							}
							addBuff(buffid, bufflv, ticks);
						}
					}
				}
			}

			pFromActor->getLivingAttrib()->checkAllStatusClearFlags(2);
		}
	}

	if (hpdec > 0 /*&& isNewStatus()*/) {
		int exceptId = 0;
		if (atkdata.buffId > 0) { exceptId = GetDefManagerProxy()->getRealStatusId(atkdata.buffId, atkdata.buffLevel); }
		checkAllStatusClearFlags(3, exceptId);
	}

	ActorLiving *liver = dynamic_cast<ActorLiving*>(attacker);
	if (liver) {
		LivingAttrib* attr = dynamic_cast<LivingAttrib*>(liver->getAttrib());
		if (attr && attr->getEquipItemWithType(EQUIP_WEAPON) == 12316) {//冰棒
			addTemperature(-3, false);
		} 
	}

	return true;
}

float LivingAttrib::calculateHpdecOld(OneAttackData& atkdata, ClientActor* attacker /*= NULL*/)
{
	//TODO:效果影响攻击者的攻击力
	if (attacker)
	{
		LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(attacker->getAttrib());
		if (pAttrib)
			atkdata.atkpoints = pAttrib->getAttackPointWithStatus(atkdata.atktype, atkdata.atkpoints);
	}

	int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atktype) : atkdata.atktype);

	float hpdec = 0;
	//float enchant_protect = getEnchantArmorPoint(atkdata.atktype) * 0.04f * (GenRandomFloat()*0.5f + 0.5f);
	float enchant_protect = (getEnchantArmorPoint((ATTACK_TYPE)iAttackType) / (getEnchantArmorPoint((ATTACK_TYPE)iAttackType) + 20)) * (GenRandomFloat() * 0.5f + 0.5f); // modify by null , 修改伤害计算公式 
	enchant_protect = Rainbow::Clamp(enchant_protect, 0.0f, 1.0f);
	//float buff_add = atkdata.buff_atk + getModAttrib(MODATTR_DAMAGED_PUNCH + iAttackType);
	float buff_add = atkdata.buff_atk;
	if (MODATTR_DAMAGED_PUNCH + iAttackType < MAX_MOD_ATTRIB)
		buff_add += getModAttrib(MODATTR_DAMAGED_PUNCH + iAttackType);

	if (buff_add < -1.0f)
		buff_add = -1.0f;

	ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	float hurtinc = 1.0f;
	float hurtdec = 1.0f;
	//float armor = getArmorPoint(atkdata.atktype) * 0.05f;
	float armor = getArmorPoint((ATTACK_TYPE)iAttackType);
	armor = getAromrPointWithStatus(atkdata.atktype, armor);	//TODO:效果影响防御点数
	armor = armor / (armor + 20); // modify by null, 修改伤害计算公式 

	if (iAttackType < MAX_PHYSICS_ATTACK)
	{
		float criticalfactor = atkdata.critical ? 1.5f : 1.0f;
		float baseatk = 0.0f;

		int atktypeoffset = iAttackType - ATTACK_PUNCH;
		ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
		auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
		if (fromplayer)
		{
			baseatk += 1.0f * GetLuaInterfaceProxy().get_lua_const()->kongshou_shanghai_beilv + fromplayer->getGeniusValue(GENIUS_BASEATK_INC); //modify by null  乘以空手伤害倍率

			hurtinc += (geniusMgr != nullptr) ? geniusMgr->getGeniusHurtInc(
				fromplayer,
				(PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_INC + atktypeoffset),
				GENIUS_PHYSICSHURT_INC) : 0.0;
		}

		if (thisplayer)
		{
			hurtdec -= (geniusMgr != nullptr) ? geniusMgr->getGeniusHurtDec(
				thisplayer,
				(PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_DEC + atktypeoffset),
				GENIUS_PHYSICSHURT_DEC) : 0.0;
		}

		hpdec = (atkdata.atkpoints * (1.0f - armor) + baseatk + atkdata.enchant_atk) * (1.0f + buff_add) * criticalfactor * (1.0f - enchant_protect); //伤害公式

		//damageArmor(atkdata.atkpoints*(1.0f - armor)*2.0f);
	}
	else
	{
		ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
		int atktypeoffset = iAttackType - ATTACK_FIRE;
		if (iAttackType < MAX_MAGIC_ATTACK)
		{
			hpdec = atkdata.atkpoints * (1.0f - armor) * (1.0f + buff_add) * (1.0f - enchant_protect);
		}
		else hpdec = atkdata.atkpoints;
		auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
		if (fromplayer)
		{
			hurtinc += (geniusMgr != nullptr) ? geniusMgr->getGeniusHurtInc(
				fromplayer,
				(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_INC + atktypeoffset),
				GENIUS_MAGICHURT_INC) : 0.0;
		}
		if (thisplayer)
		{
			hurtdec -= (geniusMgr != nullptr) ? geniusMgr->getGeniusHurtDec(
				thisplayer,
				(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_DEC + atktypeoffset),
				GENIUS_MAGICHURT_DEC) : 0.0;
		}

		//damageArmor(hpdec);
	}

	//TODO:效果影响受到的伤害值
	hpdec = getHurtPointWithStatus(atkdata.atktype, hpdec, attacker);

	hpdec *= hurtinc * hurtdec;
	hpdec = manageShieldLife(hpdec);//吸收伤害

	// 熟练度系统 - 皮肤加成到最终伤害
	hpdec = getWeaponSkinAddition(hpdec, dynamic_cast<ClientPlayer*>(atkdata.fromplayer));

	return hpdec;
}

float LivingAttrib::calculateHpdecNew(OneAttackData& atkdata, ClientActor* attacker /*= NULL*/)
{
	// 初始化蓄力修正，技能伤害倍率，范围伤害衰减系数
 	if (atkdata.damping < 0.00001f)
	{
		atkdata.damping = 1.f;
	}
	if (atkdata.charge < 0.00001f)
	{
		atkdata.charge = 1.f;
	}
	if (atkdata.skillDamageIns < 0.00001f)
	{
		atkdata.skillDamageIns = 1.0f;
	}

	ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	ClientMob* thismob = dynamic_cast<ClientMob*>(m_OwnerActor);

	// 获取投掷物的投掷者
	ClientActorProjectile* proj = dynamic_cast<ClientActorProjectile*>(attacker);
	if (proj)
	{
		attacker = m_OwnerActor->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(proj->m_ShootingActorID);
	}

	// 攻击类型
	int atkTypeNew = atkdata.atkTypeNew;
	// 触发器设置的攻击类型
	int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType() : -1);
	if (iAttackType >= 0)
	{
		int index = AtkType2ArmorIndex((ATTACK_TYPE)iAttackType);
		if ((1 << index) != atkdata.atkTypeNew)
		{
			// 转换攻击类型和攻击力
			if (iAttackType == PHYSICS_ATTACK)
			{
				iAttackType = ATTACK_PUNCH;
			}

			atkTypeNew = (1 << index);
			float totalAtkPoints = 0.f;
			for (int i = 0; i < 10; i++)
			{
				totalAtkPoints += atkdata.atkPointsNew[i];
			}
			for (int i = 0; i < 7; i++)
			{
				totalAtkPoints += atkdata.explodePoints[i];
			}

			memset(atkdata.atkPointsNew, 0, sizeof(atkdata.atkPointsNew));
			memset(atkdata.explodePoints, 0, sizeof(atkdata.explodePoints));
			// 独立爆炸
			if (iAttackType == ATTACK_EXPLODE)
			{
				atkdata.explodePoints[0] = totalAtkPoints;
			}
			else
			{
				atkdata.atkPointsNew[index] = totalAtkPoints;
			}
		}
	}

	LivingAttrib* atkAttrib = nullptr;
	if (attacker)
	{
		ActorLiving* living = dynamic_cast<ActorLiving*>(attacker);
		if (living)
		{
			atkAttrib = living->getLivingAttrib();
		}
	}

	// 伤害值
	float hpdec = 0.f;

	//固定受击伤害
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_FIXED_HURT, vValue);
	if (vValue.size() > 0)
	{
		hpdec = getActorAttValueWithStatus(BUFFATTRT_FIXED_HURT, 0);

		return hpdec;
	}

	// 所有伤害百分比加成、减免
	float damageInc = 0.f, damageDec = 0.f;

	// buff对所有伤害百分比加成
	if(atkAttrib && !atkdata.isNotInherit)
		damageInc += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ALL_DAMAGE, true);

	// buff对所有伤害百分比减免
	damageDec = getActorAttValueWithStatusByType(BUFFATTRT_ALL_HURT, true);

	// 攻击方是玩家 - 武器皮肤百分比加成（所有伤害）
	auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
	if (fromplayer != nullptr)
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			//皮肤加成1 - 增加X%的伤害
			const char* attrType1 = "WeaponSkin_System_AttackDamage";
			float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType1, fromplayer);

			damageInc += fpercent;
		}
	}

	//承伤方是玩家 - 皮肤加成/伤害百分比减免（所有伤害）
	if (thisplayer != nullptr)
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			const char* attrType = "WeaponSkin_System_ReduceDamage";
			float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, thisplayer);
			if (fpercent > 0)
			{
				fpercent = -fpercent;
			}
			damageDec += fpercent;
		}
	}

	//所有伤害减免附魔 所有伤害减免符文 renjie
	{
		damageDec -= getEnchantDamageDecPer(ENCHANT_DAMAGE_DEC,ATTACK_ALL);
	}

	// 暴击修正
	float criticalfactor = 1.0f;
	if (atkdata.critical)
		criticalfactor = 2.0f;  // 暴击调整为2倍

	// 默认攻击力
	float baseatk = 0.f, atkPoint = 0.f;
	// 默认攻击和天赋增加攻击力
	//auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
	if (fromplayer)
	{
		baseatk += 1.0f * GetLuaInterfaceProxy().get_lua_const()->kongshou_shanghai_beilv + fromplayer->getGeniusValue(GENIUS_BASEATK_INC);
	}
	else
	{
		// 头部暴击
		if (atkAttrib)
		{
			atkdata.damageFactor += atkAttrib->getActorAttValueWithStatus(BUFFATTRT_FIREARM_HEADDAMAGE_PERCENT, atkdata.damageFactor);
		}
	}

	// 针对特定生物伤害加成、减免
	float targetDamageInc = 0.f, targetDamageDec = 0.f;

	// 对特定生物伤害百分比加成
	targetDamageInc = getTargetDamageIns(attacker, true);

	// 对特定生物伤害百分比减免
	targetDamageDec = getTargetDamageDec(attacker, true);

	// 元素抗性系数
	int magicResistCoefficient = GetLuaInterfaceProxy().get_lua_const()->magicResistCoefficient;
	if (magicResistCoefficient == 0) magicResistCoefficient = 50;

	// 是否是独立爆炸
	bool isSingleExplode = false;
	// 是否触发免疫
	bool isTriggerImmune = false;

	float damagePartDec = 0;
	// 爆炸
	int flag = (1 << ATTACK_EXPLODE);
	if ((atkTypeNew & flag) > 0)
	{
		// 爆炸免疫
		if (hasImmuneType(getImmuneTypeByAttackType(ATTACK_EXPLODE)))
		{
			isTriggerImmune = true;
			return hpdec;
		}

		// buff、生物属性对爆炸伤害百分比加成、减免
		float explodeIns = 0.f, explodeDec = 0.f;
		if (atkAttrib && !atkdata.isNotInherit)
		{
			explodeIns = atkAttrib->getModAttrib(MODATTR_ATTACK_EXPLODE) +
				atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_EXPLODE_DAMAGE, true);
		}
		explodeDec += getModAttrib(MODATTR_DAMAGED_EXPLODE) + getActorAttValueWithStatusByType(BUFFATTRT_EXPLODE_HURT, true);
		explodeDec += getArmorPoint(ATTACK_EXPLODE);

		//爆炸伤害减免附魔 爆炸伤害减免符文 renjie
		// {
		// 	explodeDec -= getEnchantDamageDecPer(ENCHANT_EXPLODE_DEC, ATTACK_EXPLODE);
		// }

		ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
		
		/*
		* 独立爆炸 ================================================================================================================
		* 物理伤害 = 爆炸物物理攻击 × (1 - 物理防御减伤比%) × (1 + 爆炸增伤%) × (1 - 爆炸减伤%) × (1 - 伤害减免%)
		* 元素伤害 = 爆炸物元素攻击 × (1 - 元素抗性减伤比%) × (1 - 元素防御减伤比%) × (1 + 爆炸增伤%) × (
		*	1 - 爆炸减伤%) × (1 - 伤害减免%) × (1 -  燃烧/混乱/毒素/冰冻伤害减免%) + 单元素受击固定伤害
		* 总伤害 = 物理伤害 + 元素伤害 + 爆炸受击固定伤害
		*
		* 物理防御减伤比% = (物理防御 + 爆炸防御) ÷ ((物理防御 + 爆炸防御)  + 物理防御系数)，其中物理防御系数=30(现为20)
		*
		* 元素防御减伤比% = (元素防御 + 爆炸防御) ÷ ((元素防御 + 爆炸防御) + 元素防御系数)，其中元素防御系数=30(现为20)
		*
		* 元素抗性减伤比% = 元素抗性 ÷ (元素抗性 + 元素抗性系数)，其中元素抗性系数=50(暂定，可配置)
		*
		* 爆炸的攻击仅从爆炸物本身获取，不受其他系统成长影响
		*/
		if ((atkTypeNew & ~flag) == 0)
		{
			isSingleExplode = true;
			// 天赋对爆炸伤害、物理伤害百分比减免
			// if (geniusMgr)
			// {
			// 	float explodeDecG = 0.f;
			// 	if (thisplayer)
			// 		explodeDecG = geniusMgr->getGeniusHurtDec(thisplayer, (PLAYER_GENIUS_TYPE)(GENIUS_EXPLODEHURT_DEC),
			// 			GENIUS_PHYSICSHURT_DEC);
			//
			// 	if (explodeDecG > 0)
			// 	{
			// 		explodeDecG = -explodeDecG;
			// 	}
			//
			// 	explodeDec += explodeDecG;
			// }

			// 物理伤害公式
			hpdec += atkdata.explodePoints[ATTACK_PUNCH] * (1 + explodeDec) * (1 + damageDec);

			// 元素防御 + 所有防御
			float magicArmor = getArmorPointSingle(MAX_MAGIC_ATTACK);
			magicArmor = getAromrPointWithStatus(MAX_MAGIC_ATTACK, magicArmor);
			magicArmor += getEnchantArmorPoint(MAX_MAGIC_ATTACK);

			// 爆炸防御 
			float explodeArmor = getArmorPointSingle(ATTACK_EXPLODE);
			explodeArmor = getAromrPointWithStatusSingle(ATTACK_EXPLODE, explodeArmor);
			explodeArmor += getEnchantArmorPoint(ATTACK_EXPLODE, false);

			// 元素防御减伤比
			magicArmor = (magicArmor + explodeArmor) / (magicArmor + explodeArmor + 30);

			float magicDamageDec = 0.f;
			for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
			{
				// 独立爆炸攻击力，火的下标是1，这里用攻击类型做下标需要-2
				atkPoint = atkdata.explodePoints[atkType - 2];
				if (atkPoint < 0.00001f) continue;

				// // 单元素抗性（单元素防御）
				// if (atkType < MAX_MAGIC_ATTACK)
				// {
				// 	armor = getArmorPointSingle((ATTACK_TYPE)atkType);
				// 	armor = getAromrPointWithStatusSingle((ATTACK_TYPE)atkType, armor);
				// 	armor += getEnchantArmorPoint((ATTACK_TYPE)atkType, false);
				// 	armor = armor / (armor + magicResistCoefficient);
				// }
				// else
				// 	armor = 0.f;

				magicDamageDec = 0.f;
				if (atkType == ATTACK_ICE)
				{
					// buff对冰伤害百分比减免（无生物属性对冰伤害百分比减免）
					magicDamageDec = getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, true);

					// buff对冰伤害受击固定点加成
					hpdec += getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, false);
				}
				else if (atkType < ATTACK_FLASH)
				{
					// // 天赋对单元素伤害百分比减免
					// if (geniusMgr)
					// {
					// 	if (thisplayer)
					// 		magicDamageDec += geniusMgr->getGeniusHurtDec(thisplayer,
					// 			(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_DEC + atkType - ATTACK_FIRE),
					// 			GENIUS_MAGICHURT_DEC);

					// 	if (magicDamageDec > 0)
					// 	{
					// 		magicDamageDec = -magicDamageDec;
					// 	}
					// }

					// 生物属性、buff对单元素伤害百分比加成
					magicDamageDec = getModAttrib(MODATTR_DAMAGED_FIRE + atkType - ATTACK_FIRE)
						+ getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, true);

					// buff对单元素受击固定点加成
					hpdec += getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, false);
				}

				// 伤害计算
				damageInc += explodeIns;
				damageDec += explodeDec + magicDamageDec;
				float factor = 1.0f; 
				hpdec += atkPoint * (1 + damageInc) * (1 + damageDec) * factor;
			}
		}
		else
		{
			/*
			* 物理爆炸================================================================================================================
			* 物理爆炸伤害 = ((物理攻击 + 爆炸物物理攻击 + 爆炸攻击 + 所有攻击) × (1 - 物理防御减伤比%))× 技能伤害倍率% 
			*	× (1 + 伤害加成%) × (1 + 伤害减免%) × (1 + 爆炸增伤%) × (1 + 爆炸减伤%)
			*	× 蓄力修正%
			* 
			* 物理防御减伤比% = (物理防御 + 爆炸防御 + 所有防御) ÷ ((物理防御 + 爆炸防御 + 所有防御)  + 物理防御系数)，其中物理防御系数=30(现为20)
			* 
			*/
			flag = (1 << ATTACK_PUNCH);
			auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
			if ((atkTypeNew & flag) > 0)
			{
				atkPoint = atkdata.explodePoints[ATTACK_PUNCH];
				// 爆炸攻击力、所有攻击力加成
				if (atkAttrib && !atkdata.isNotInherit)
					atkPoint = atkAttrib->getAttackPointWithStatus(ATTACK_EXPLODE, atkPoint);

				// // 天赋对物理、爆炸伤害百分比加成、减免
				// if (geniusMgr)
				// {
				// 	explodeIns += geniusMgr->getGeniusHurtInc(fromplayer, (PLAYER_GENIUS_TYPE)(GENIUS_EXPLODEHURT_INC),
				// 		GENIUS_PHYSICSHURT_INC);

				// 	float explodeDecG = 0.f;
				// 	if (thisplayer)
				// 		explodeDecG = geniusMgr->getGeniusHurtDec(thisplayer, (PLAYER_GENIUS_TYPE)(GENIUS_EXPLODEHURT_DEC),
				// 			GENIUS_PHYSICSHURT_DEC);

				// 	if (explodeDecG > 0)
				// 	{
				// 		explodeDecG = -explodeDecG;
				// 	}

				// 	explodeDec += explodeDecG;
				// }

				// 伤害计算
				damageInc += explodeIns;
				damageDec += explodeDec;
				float factor = 1.0f; // atkdata.charge* atkdata.skillDamageIns;
				hpdec += atkPoint * (1 + damageInc) * (1 + damageDec) * factor;
			}
			/*
			* 元素爆炸================================================================================================================
			* 元素爆炸伤害 = (元素总和攻击 × (1 - 元素抗性减伤比%) × (1 - 元素防御减伤比%))× 技能伤害倍率% 
			*	× (1 + 伤害加成%) × (1 + 伤害减免%) × (1 + 爆炸增伤%) × (1 + 爆炸减伤%) 
			*   × (1 + 燃烧/混乱/毒素/冰冻伤害加成%) × (1 + 燃烧/混乱/毒素/冰冻伤害减免%)
			*	× 蓄力修正% + 元素类固定伤害
			* 
			* 无对应元素类型不计算燃烧/混乱/毒素/冰冻元素抗性减伤比%、伤害加成%及其伤害减免%
			* 
			* 元素总和攻击 = 元素攻击 + 燃烧/混乱/毒素/冰冻攻击 + 爆炸攻击 + 所有攻击 ，每一种元素单独计算伤害及其抗性，
			* 以攻击时武器携带的元素为计算基准，其中爆炸物也可提供上述攻击
			* 
			* 元素防御减伤比% = (元素防御 + 爆炸防御 + 所有防御) ÷ ((元素防御 + 爆炸防御 + 所有防御) + 元素防御系数)，其中元素防御系数=30(现为20)
			* 
			* 各元素抗性减伤比% = 燃烧/混乱/毒素/冰冻抗性 ÷ (燃烧/混乱/毒素/冰冻抗性 + 元素抗性系数)，其中元素抗性系数=50(暂定，可配置)
			* 
			*/
			else
			{
				// 天赋对爆炸伤害百分比加成、减免
				// if (geniusMgr)
				// {
				// 	explodeIns += geniusMgr->getGeniusHurtInc(fromplayer, (PLAYER_GENIUS_TYPE)(GENIUS_EXPLODEHURT_INC),
				// 		GENIUS_EXPLODEHURT_INC);

				// 	float explodeDecG = 0.f;
				// 	if (thisplayer)
				// 		explodeDecG += geniusMgr->getGeniusHurtDec(thisplayer, (PLAYER_GENIUS_TYPE)(GENIUS_EXPLODEHURT_DEC),
				// 			GENIUS_EXPLODEHURT_DEC);


				// 	if (explodeDecG > 0)
				// 	{
				// 		explodeDecG = -explodeDecG;
				// 	}

				// 	explodeDec += explodeDecG;
				// }

				// 元素防御 + 所有防御
				float magicArmor = getArmorPointSingle(MAX_MAGIC_ATTACK);
				magicArmor = getAromrPointWithStatus(MAX_MAGIC_ATTACK, magicArmor);
				magicArmor += getEnchantArmorPoint(MAX_MAGIC_ATTACK);

				// 爆炸防御 
				float explodeArmor = getArmorPointSingle(ATTACK_EXPLODE);
				explodeArmor = getAromrPointWithStatusSingle(ATTACK_EXPLODE, explodeArmor);
				explodeArmor += getEnchantArmorPoint(ATTACK_EXPLODE, false);

				// 元素防御减伤比
				magicArmor = (magicArmor + explodeArmor) / (magicArmor + explodeArmor + 30);

				float magicDamageIns = 0.f, magicDamageDec = 0.f;
				for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
				{
					// 独立爆炸攻击力，火的下标是1，这里用攻击类型做下标需要-2
					atkPoint = atkdata.explodePoints[atkType - 2];
					if (atkPoint < 0.00001f) continue;

					// 爆炸攻击力、元素攻击力、所有攻击力加成
					if (atkAttrib && !atkdata.isNotInherit)
					{
						// 爆炸攻击力加成
						atkPoint = atkAttrib->getAttackPointWithStatusSingle(ATTACK_EXPLODE, atkPoint);
						// 元素攻击力、所有攻击力加成
						atkPoint = atkAttrib->getAttackPointWithStatus((ATTACK_TYPE)atkType, atkPoint);
					}

					// // 单元素抗性（单元素防御）
					// if (atkType < MAX_MAGIC_ATTACK)
					// {
					// 	armor = getArmorPointSingle((ATTACK_TYPE)atkType);
					// 	armor = getAromrPointWithStatusSingle((ATTACK_TYPE)atkType, armor);
					// 	armor += getEnchantArmorPoint((ATTACK_TYPE)atkType, false);
					// 	armor = armor / (armor + magicResistCoefficient);
					// }
					// else
					// 	armor = 0.f;

					//  生物属性、buff对冰元素伤害加成、减免
					magicDamageDec = 0.f;
					magicDamageIns = 0.f;
					if (atkType == ATTACK_ICE)
					{
						if (atkAttrib && !atkdata.isNotInherit)
						{
							magicDamageIns = atkAttrib->getModAttrib(MODATTR_ATTACK_ICE)
								+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ICE_HARM, true);
						}
						magicDamageDec = getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, true);

						// buff对冰伤害固定点加成
						if (atkAttrib && !atkdata.isNotInherit)
							hpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ICE_HARM, false);
						hpdec += getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, false);
					}
					else if (atkType < ATTACK_FLASH)
					{
						// 天赋对单元素伤害百分比加成、减免
						// if (geniusMgr)
						// {
						// 	magicDamageIns += geniusMgr->getGeniusHurtInc(fromplayer,
						// 		(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_INC + atkType - ATTACK_FIRE),
						// 		GENIUS_MAGICHURT_DEC);

						// 	if (thisplayer)
						// 		magicDamageDec += geniusMgr->getGeniusHurtDec(thisplayer,
						// 			(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_DEC + atkType - ATTACK_FIRE),
						// 			GENIUS_MAGICHURT_DEC);

						// 	if (magicDamageDec > 0)
						// 	{
						// 		magicDamageDec = -magicDamageDec;
						// 	}
						// }

						// buff、属性对单元素伤害百分比加成减免
						if (atkAttrib && !atkdata.isNotInherit)
						{
							magicDamageIns = atkAttrib->getModAttrib(MODATTR_ATTACK_FIRE + atkType - ATTACK_FIRE)
								+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_FIRE_DAMAGE + atkType - ATTACK_FIRE, true);
						}
						magicDamageDec = getModAttrib(MODATTR_DAMAGED_FIRE + atkType - ATTACK_FIRE)
							+ getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, true);

						// buff对单元素伤害固定点加成
						if (atkAttrib && !atkdata.isNotInherit)
							hpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_FIRE_DAMAGE + atkType - ATTACK_FIRE, false);
						hpdec += getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, false);
					}

					// 元素伤害
					damageInc += explodeIns + magicDamageIns;
					damageDec += explodeDec + magicDamageDec;
					float factor = 1.0f;   // atkdata.charge * atkdata.skillDamageIns
					hpdec += atkPoint * (1 + damageInc)
						* (1 + damageDec) * factor;
				}
			}

			// buff对爆炸伤害固定点加成
			if (atkAttrib && !atkdata.isNotInherit)
				hpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_EXPLODE_DAMAGE, false);
		}

		// buff对爆炸受击伤害固定点加成
		hpdec += getActorAttValueWithStatusByType(BUFFATTRT_EXPLODE_HURT, false);

		// 爆炸受击伤害buff移除
		damageRemoveBuff(hpdec, ATTACK_EXPLODE, 0);
	}
	else
	{
		/*
		* 近战物理================================================================================================================
		* 物理伤害 ： ((物理攻击 + 近程物理攻击 + 所有攻击) × (1 + 连段攻击倍率%) × (1 - 物理防御减伤比%) × 技能伤害倍率% 
		*	) × (1 + 伤害加成%) × (1 + 伤害减免%) × (1 + 近程物理伤害加成%) × (1 + 近程物理伤害减免%) 
		*	× (1 + 魔物/动物/人类/野人伤害加成%) × (1 + 魔物/动物/人类/野人伤害减免%) × 暴击修正% × 
		*	蓄力修正% × 伤害修正% + 近战物理固定伤害
		* 
		* 注1：如果固定伤害进行了类型区分则需要对各伤害来源进行区分计算，下同
		* 注2：伤害减免在描述中表达为“使角色受到XX伤害-20%”，下同
		* 注3：非蓄力技能不计算蓄力修正%
		* 注4：无对应怪物类型不计算魔物/动物/人类/野人伤害加成%及伤害减免%
		* 
		* 物理防御减伤比% =  (物理防御 + 近程物理防御 + 所有防御) ÷ ((物理防御 + 近程物理防御 + 所有防御)  + 物理防御系数)，其中物理防御系数=30(现为20)
		* 
		* 蓄力修正% = 实际蓄力时间/最大蓄力时间
		* 
		* 物理攻击、近程攻击目前可来自角色初始、武器/装备、附魔、BUFF、天赋、皮肤等
		* 
		* 所有加成/减免属性均以属性条目为依据进行叠加，叠加后的值连乘处理，目前可来自附魔、BUFF、天赋、皮肤等
		* 
		* 近战跳砍击中目标或远程击中目标头部产生暴击，暴击修正%=50%
		*/
		
		flag = (1 << ATTACK_PUNCH);
		if ((atkTypeNew & flag) > 0)
		{
			do
			{
				float pHpdec = 0.f;
				// 近战攻击免疫
				if (hasImmuneType(getImmuneTypeByAttackType(ATTACK_PUNCH)))
				{
					isTriggerImmune = true;
					break;
				}
				// 物理攻击 + 近战攻击力 + 所有攻击力 + 加成攻击力
				atkPoint = atkdata.atkPointsNew[ATTACK_PUNCH];
				if (atkAttrib && !atkdata.isNotInherit)
				{
					atkPoint = atkAttrib->getAttackPointWithStatus(ATTACK_PUNCH, atkPoint);
				}

				// 近战物理伤害加成、减免
				float punchDamageInc = 0.f, punchDamageDec = 0.f;

				// 天赋对近战物理伤害百分比加成、减免
				// ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
				// if (geniusMgr)
				// {
				// 	auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
				// 	punchDamageInc += geniusMgr->getGeniusHurtInc(fromplayer, (PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_INC),
				// 		GENIUS_PHYSICSHURT_INC);

				// 	if (thisplayer)
				// 		punchDamageDec += geniusMgr->getGeniusHurtDec(thisplayer, (PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_DEC),
				// 			GENIUS_PHYSICSHURT_DEC);

				// 	if (punchDamageDec > 0)
				// 	{
				// 		punchDamageDec = -punchDamageDec;
				// 	}
				// }

				// 生物属性、buff对近战物理伤害百分比加成、减免
				if (atkAttrib && !atkdata.isNotInherit)
				{
					punchDamageInc += atkAttrib->getModAttrib(MODATTR_ATTACK_PUNCH)
						+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_MELEE_DAMAGE, true);
				}
				punchDamageDec += getActorAttValueWithStatusByType(BUFFATTRT_MELEE_HURT, true);  //getModAttrib(MODATTR_DAMAGED_PUNCH) + getActorAttValueWithStatusByType(BUFFATTRT_MELEE_HURT, true);


				//来自攻击方的近程攻击符文 近程攻击附魔 伤害加成 renjie
				// if (atkAttrib && !atkdata.isNotInherit)
				// {
				// 	float punchDamageEnchant = atkAttrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_PUNCH_INC, ATTACK_PUNCH, ATTACK_TARGET_ALL);//近程攻击伤害加成
				// 	punchDamageInc += punchDamageEnchant;
				// }

				// 伤害计算
				damagePartDec = -getArmorPointByPart(ATTACK_PUNCH, atkdata.parttype);
				damageInc += math::min(0.0f, punchDamageInc + targetDamageInc);
				damageDec += math::max(-1.0f, damagePartDec + punchDamageDec);   // punchDamageDec + targetDamageDec;
				float factor = 1.0f; // atkdata.charge* atkdata.skillDamageIns* atkdata.damageFactor;
				hpdec += atkPoint * (1 + damageInc) * (1 + damageDec) * criticalfactor * factor;
				if (hpdec < 0) hpdec = 0;

				// buff对近战伤害固定点加成
				if (atkAttrib && !atkdata.isNotInherit)
				{
					pHpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_MELEE_DAMAGE, false);
				}
				pHpdec += getActorAttValueWithStatusByType(BUFFATTRT_MELEE_HURT, false);

				hpdec += pHpdec;

				// 近战受击伤害buff效果移除
				damageRemoveBuff(pHpdec, ATTACK_PUNCH, 0);
			} while (false);
		}
		/*
		* 远程物理================================================================================================================
		* 公式 （以弓为例）：((物理攻击 + 弓物理攻击 + 箭物理攻击 + 远程物理攻击 + 所有攻击) × (1 - 物理防御减伤比%)) × 技能伤害倍率% 
		*	× (1 + 伤害加成%) × (1 + 伤害减免%) × (1 + 远程物理伤害加成%) × (1 + 远程物理伤害减免%) 
		*	× (1 + 魔物/动物/人类/野人伤害加成%) × (1 + 魔物/动物/人类/野人伤害减免%) × (1 + 弓箭伤害加成%) 
		*	× 暴击修正% × 蓄力修正% × 伤害修正% + 远程物理固定伤害
		* 
		* 物理防御减伤比% = (物理防御 + 远程防御) ÷ ((物理防御 + 远程防御)  + 物理防御系数)，其中物理防御系数=30(现为20)
		* 
		* 物理类的弓，射出了元素类的箭，则分别计算物理及元素伤害（见远程元素伤害计算）
		* 
		* 蓄力修正% = 实际蓄力时间/最大蓄力时间
		*/
		if ((atkTypeNew & (1 << ATTACK_RANGE)) > 0)
		{
			do
			{
				float rHpdec = 0.f;

				// 远程攻击免疫
				if (hasImmuneType(getImmuneTypeByAttackType(ATTACK_RANGE)))
				{
					isTriggerImmune = true;
					break;
				}
				// 物理攻击 + 远程攻击力 + 所有攻击力 + 加成攻击力
				atkPoint = atkdata.atkPointsNew[ATTACK_RANGE];
				if (atkAttrib && !atkdata.isNotInherit)
					atkPoint = atkAttrib->getAttackPointWithStatus(ATTACK_RANGE, atkPoint);

				// 远程物理伤害加成、减免
				float rangeDamageInc = 0.f, rangeDamageDec = 0.f;

				// 天赋对远程物理伤害百分比加成、减免
				// ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
				// if (geniusMgr)
				// {
				// 	auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
				// 	rangeDamageInc += geniusMgr->getGeniusHurtInc(fromplayer, (PLAYER_GENIUS_TYPE)(GENIUS_RANGEHURT_INC),
				// 		GENIUS_PHYSICSHURT_INC);

				// 	if (thisplayer)
				// 		rangeDamageDec += geniusMgr->getGeniusHurtDec(thisplayer, (PLAYER_GENIUS_TYPE)(GENIUS_RANGEHURT_DEC),
				// 			GENIUS_PHYSICSHURT_DEC);

				// 	if (rangeDamageDec > 0)
				// 	{
				// 		rangeDamageDec = -rangeDamageDec;
				// 	}
				// }

				// 生物属性、buff对远程物理伤害百分比加成、减免
				if (atkAttrib && !atkdata.isNotInherit)
				{
					rangeDamageInc = atkAttrib->getModAttrib(MODATTR_ATTACK_RANGE)
						+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_REMOTE_DAMAGE, true);
				}
				rangeDamageDec += getModAttrib(MODATTR_DAMAGED_RANGE)
					+ getActorAttValueWithStatusByType(BUFFATTRT_REMOTE_HURT, true);

				//远程攻击伤害减免附魔 远程攻击伤害减免符文 renjie
				// {
				// 	float rangeDamageEnchant = getEnchantDamageDecPer(ENCHANT_RANGE_DAMAGE_DEC, ATTACK_RANGE);
				// 	rangeDamageDec -= rangeDamageEnchant;
				// }

				// 对有特定buff生物概率造成特定伤害加成
				std::vector<StatusAttInfo> vValue;
				getStatusAddAttInfo(BUFFATTRT_PROB_MOREDAMAGE_BEATTACK_HASBUFF, vValue);
				if (vValue.size() != 0)
				{
					StatusAttInfo info = vValue[0];
					int buffid = (info.vValue[0].value) / 1000;
					if (hasBuff(buffid))
					{
						int per = info.vValue[1].value;
						if (GenRandomInt(0, 100) < per)
						{
							float damageIns = info.vValue[2].value * 1.0 / 100;
							auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef((int)(info.vValue[3].value));
							if (enumdef && enumdef->AttType == BuffAttrType::BUFFATTRT_REMOTE_DAMAGE) 
							{
								rangeDamageInc += damageIns;
							}
						}
					}
				}

				// 弓箭（新枪械）伤害加成
				float bowDamageIns = atkdata.bowDamageIns;
				if (atkAttrib)
				{
					bowDamageIns += atkAttrib->getModAttrib(MOBATTR_ATTACK_GUN) 
						+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ATTACK_GUN, true);
				}

				// 伤害计算
				damagePartDec = -getArmorPointByPart(ATTACK_RANGE, atkdata.parttype);
				damageInc += rangeDamageInc + targetDamageInc + bowDamageIns;
				damageDec += math::max(-1.0f, rangeDamageDec + targetDamageDec + damagePartDec);
				float factor = 1.0f; // atkdata.charge* atkdata.skillDamageIns* atkdata.damageFactor;
				rHpdec += atkPoint * (1 + damageInc) * (1 + damageDec) * criticalfactor * factor;
				if (rHpdec < 0) rHpdec = 0;

				// buff对近战伤害固定点加成
				if (atkAttrib && !atkdata.isNotInherit)
				{
					rHpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_MELEE_DAMAGE, false);
				}
				rHpdec += getActorAttValueWithStatusByType(BUFFATTRT_MELEE_HURT, false);

				hpdec += rHpdec;

				// 远程受击伤害buff效果移除
				damageRemoveBuff(rHpdec, ATTACK_PUNCH, 0);

			} while (false);
		}

		// 元素伤害================================================================================================================
		// flag = (1 << (ATTACK_FIRE + 1)) | (1 << (ATTACK_POISON + 1)) | (1 << (ATTACK_WITHER + 1)) | (1 << (ATTACK_FLASH + 1)) | (1 << (ATTACK_ICE + 1)) | (1 << (MAX_MAGIC_ATTACK + 1));
		// if ((atkTypeNew & flag) > 0)
		// {
		// 	/*
		// 	* 公式 ：(元素总和攻击 × (1 - 各元素抗性减伤比%) × (1 - 元素防御减伤比%)) × 技能伤害倍率% × 
		// 	*	(1 + 伤害加成%) × (1 + 伤害减免%) × (1 + 魔物/动物/人类/野人伤害加成%) × 
		// 	*	(1 + 魔物/动物/人类/野人伤害减免%) × (1 + 弓箭伤害加成%) × (1 + 燃烧/混乱/毒素/冰冻伤害加成%) ×
		// 	*	(1 + 燃烧/混乱/毒素/冰冻伤害加成%) × 蓄力修正% × 伤害修正%  + 元素类固定伤害
		// 	* 
		// 	* 注1：非弓箭造成的伤害不计算弓箭伤害加成%
		// 	* 注2：无对应怪物类型不计算魔物/动物/人类/野人伤害加成%及伤害减免%
		// 	* 注3：非蓄力技能不计算蓄力修正%
		// 	* 
		// 	* 元素总和攻击 = 元素攻击 + 燃烧/混乱/毒素/冰冻攻击 + 所有攻击，每一种元素单独计算伤害及其抗性，
		// 	*	以攻击时武器携带的元素为计算基准，原则上冒险模式不会投放元素攻击
		// 	* 
		// 	* 元素防御减伤比% =  (元素防御 + 所有防御) ÷ ((元素防御 + 所有防御) + 元素防御系数)，其中元素防御系数=30(现为20)
		// 	* 
		// 	* 元素抗性减伤比% = 燃烧/混乱/毒素/冰冻抗性 ÷ (燃烧/混乱/毒素/冰冻抗性 + 元素抗性系数)，其中元素抗性系数=50(暂定，可配置)
		// 	* 
		// 	* 蓄力修正% = 实际蓄力时间/最大蓄力时间
		// 	* 
		// 	* 元素攻击、近程攻击目前可来自角色初始、武器/装备、附魔、BUFF、天赋、皮肤等
		// 	* 
		// 	* 所有加成/减免属性均以属性条目为依据进行叠加，叠加后的值连乘处理，目前可来自附魔、BUFF、天赋、皮肤等
		// 	* 
		// 	*/

		// 	// 元素防御（魔法防御）减伤比
		// 	float magicArmor = getArmorPoint(MAX_MAGIC_ATTACK);
		// 	magicArmor = getAromrPointWithStatus(MAX_MAGIC_ATTACK, magicArmor);
		// 	magicArmor += getEnchantArmorPoint(MAX_MAGIC_ATTACK);
		// 	magicArmor = magicArmor / (magicArmor + 30);

		// 	ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);

		// 	// 弓箭（新枪械）伤害加成
		// 	float bowDamageIns = atkdata.bowDamageIns;
		// 	if (atkAttrib)
		// 	{
		// 		bowDamageIns += atkAttrib->getModAttrib(MOBATTR_ATTACK_GUN)
		// 			+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ATTACK_GUN, true);
		// 	}

		// 	float armor = 0.f;
		// 	float magicDamageIns = 0.f, magicDamageDec = 0.f;
		// 	float mHpdec = 0.f;
		// 	for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
		// 	{
		// 		// 元素伤害免疫
		// 		if (hasImmuneType(getImmuneTypeByAttackType(atkType)))
		// 		{
		// 			isTriggerImmune = true;
		// 			continue;
		// 		}

		// 		atkPoint = atkdata.atkPointsNew[atkType + 1];
		// 		if(atkPoint < 0.00001f) continue;
		// 		// 元素攻击力、所有攻击力加成
		// 		if (atkAttrib && !atkdata.isNotInherit)
		// 		{
		// 			// 元素攻击力、所有攻击力加成
		// 			atkPoint = atkAttrib->getAttackPointWithStatus((ATTACK_TYPE)atkType, atkPoint);
		// 		}

		// 		// 单元素抗性（单元素防御）
		// 		if (atkType < MAX_MAGIC_ATTACK)
		// 		{
		// 			armor = getArmorPointSingle((ATTACK_TYPE)atkType);
		// 			armor = getAromrPointWithStatusSingle((ATTACK_TYPE)atkType, armor);
		// 			armor += getEnchantArmorPoint((ATTACK_TYPE)atkType, false);
		// 			armor = armor / (armor + magicResistCoefficient);
		// 		}
		// 		else
		// 			armor = 0.f;

		// 		//  buff、生物属性对冰元素伤害加成、减免
		// 		magicDamageIns = 0.f;
		// 		magicDamageDec = 0.f;
		// 		mHpdec = 0.f;
		// 		if (atkType == ATTACK_ICE)
		// 		{
		// 			// 百分比
		// 			if (atkAttrib && !atkdata.isNotInherit)
		// 			{
		// 				magicDamageIns = atkAttrib->getModAttrib(MODATTR_ATTACK_ICE)
		// 					+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ICE_HARM, true);
		// 			}
		// 			magicDamageDec = getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, true);

		// 			// buff-固定点
		// 			if (atkAttrib && !atkdata.isNotInherit)
		// 				mHpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ICE_HARM, false);
		// 			mHpdec += getActorAttValueWithStatusByType(BUFFATTRT_ICE_TEMPERATURE, false);
		// 		}
		// 		//  buff、生物属性、天赋对单元素伤害加成、减免
		// 		else if (atkType < ATTACK_FLASH)
		// 		{
		// 			// 天赋-百分比
		// 			if (geniusMgr)
		// 			{
		// 				auto fromplayer = static_cast<ClientPlayer*>(atkdata.fromplayer);
		// 				magicDamageIns += geniusMgr->getGeniusHurtInc(fromplayer, 
		// 					(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_INC + atkType - ATTACK_FIRE),
		// 					GENIUS_MAGICHURT_DEC);

		// 				if (thisplayer)
		// 					magicDamageDec += geniusMgr->getGeniusHurtDec(thisplayer,
		// 						(PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_DEC + atkType - ATTACK_FIRE),
		// 						GENIUS_MAGICHURT_DEC);

		// 				if (magicDamageDec > 0)
		// 				{
		// 					magicDamageDec = -magicDamageDec;
		// 				}
		// 			}

		// 			// buff、生物属性-百分比
		// 			if (atkAttrib && !atkdata.isNotInherit)
		// 			{
		// 				magicDamageIns += atkAttrib->getModAttrib(MODATTR_ATTACK_FIRE + atkType - ATTACK_FIRE)
		// 					+ atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_FIRE_DAMAGE + atkType - ATTACK_FIRE, true);
		// 			}
		// 			magicDamageDec += getModAttrib(MODATTR_DAMAGED_FIRE + atkType - ATTACK_FIRE)
		// 				+ getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, true);

		// 			// 对有特定buff生物概率造成特定伤害加成
		// 			std::vector<StatusAttInfo> vValue;
		// 			getStatusAddAttInfo(BUFFATTRT_PROB_MOREDAMAGE_BEATTACK_HASBUFF, vValue);
		// 			if (vValue.size() > 3)
		// 			{
		// 				StatusAttInfo info = vValue[0];
		// 				int buffid = (info.vValue[0].value) / 1000;
		// 				if (hasBuff(buffid))
		// 				{
		// 					int per = info.vValue[1].value;
		// 					if (GenRandomInt(0, 100) < per)
		// 					{
		// 						float damageIns = info.vValue[2].value * 1.0 / 100;
		// 						auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef((int)(info.vValue[3].value));
		// 						if (enumdef && enumdef->AttType == (BuffAttrType::BUFFATTRT_FIRE_DAMAGE + atkType - ATTACK_FIRE))
		// 						{
		// 							magicDamageIns += damageIns;
		// 						}
		// 					}
		// 				}
		// 			}

		// 			// buff-固定点
		// 			if (atkAttrib)
		// 				mHpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_FIRE_DAMAGE + atkType - ATTACK_FIRE, false);
		// 			mHpdec += getActorAttValueWithStatusByType(BUFFATTRT_FIRE_HURT + atkType - ATTACK_FIRE, false);
		// 		}

		// 		// 伤害计算
		// 		mHpdec += (atkPoint * (1 - armor) * (1 - magicArmor)) * (1 + damageInc) * atkdata.skillDamageIns
		// 			* (1 + damageDec) * (1 + targetDamageInc) * (1 + targetDamageDec) * (1 + magicDamageIns) * (1 + bowDamageIns)
		// 			* (1 + magicDamageDec) * atkdata.charge * atkdata.damageFactor;

		// 		hpdec += mHpdec;

		// 		if (atkType == ATTACK_ICE || atkType < ATTACK_FLASH)
		// 		{
		// 			// 火、毒、混乱、冰受击伤害buff移除
		// 			damageRemoveBuff(mHpdec, atkType, 0);
		// 		}
		// 	}
		// }
		
		if (atkAttrib && !atkdata.isNotInherit)
		{
			//ENCHANT_ADDBUFF 概率给对方增加buff
			bool haveAddBuff = atkAttrib->checkEnchant(ENCHANT_ADDBUFF, ATTACK_ALL, ATTACK_TARGET_ALL);
			if (haveAddBuff)
			{
				float BuffVal = 1009006.0f;
				float addBuffRace = atkAttrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ADDBUFF, ATTACK_ALL, ATTACK_TARGET_ALL,&BuffVal);
				if (GenRandomInt(0, 100) < addBuffRace * 100)
				{
					int buffId = BuffVal / 1000;
					int buffLv = int(round(BuffVal)) % 10;//float型需要四舍五入
					ActorBoss* Boss = dynamic_cast<ActorBoss*>(m_OwnerActor);//boss不是clientMob 增加boss类型判断
					if (!(buffId == 1003 && Boss))
					{
						addBuff(buffId, buffLv);//buffId buffLv 
					}
				}
			}
			////来自攻击方的概率使敌方变身符文 概率使敌方变身附魔 概率使敌方变身加成 renjie
			//float changeModel = atkAttrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_TRANSFORM1, ATTACK_ALL, ATTACK_TARGET_ALL);
			//if (GenRandomInt(0, 100) < changeModel*100)
			//{
			//	addBuff(1003, 1);//buffId buffLv 变嘟嘟鸟
			//}
			////- 概率迟缓：概率，百分比数值，持续时间，单位秒，使敌方的所有行为和动作变慢，不好处理可以考虑改为定帧或者移速减缓
			//bool slowDown = atkAttrib->checkEnchant(ENCHANT_SLOWDOWN_PROB, ATTACK_ALL, ATTACK_TARGET_ALL);
			//if (slowDown)
			//{
			//	float slowDownBuff = 1009006.0f;
			//	atkAttrib->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_SLOWDOWN_PROB, ATTACK_ALL, ATTACK_TARGET_ALL, &slowDownBuff);
			//	int buffId = slowDownBuff / 1000;
			//	int buffLv = int(slowDownBuff) % 10;
			//	addBuff(buffId, buffLv);//buffId buffLv 迟缓buff
			//}
		}
		

	}

	// 免疫伤害
	if (isTriggerImmune && hpdec <= std::numeric_limits<float>::epsilon())
	{
		return hpdec;
	}

	// 非独立爆炸
	if(!isSingleExplode && hpdec < 0.00001f)
		hpdec += baseatk;

	// 对特定生物伤害固定点加成
	hpdec += getTargetDamageIns(attacker, false);

	// 对特定生物伤害固定点减免
	hpdec += getTargetDamageDec(attacker, false);

	// 所有伤害固定点加成
	if (atkAttrib && !atkdata.isNotInherit)
		hpdec += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ALL_DAMAGE, false);

	// 所有伤害固定点减免
	hpdec += getActorAttValueWithStatusByType(BUFFATTRT_ALL_HURT, false);

	// 武器皮肤固定点加成
	if (atkdata.fromplayer != nullptr)
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			//皮肤加成2 - X%几率造成伤害加X
			const char* attrType2 = "WeaponSkin_System_CapableDamage";
			float fvalue = weaponSkinMgr->GetSkinAdditionValue(attrType2, static_cast<ClientPlayer*>(atkdata.fromplayer));

			hpdec += fvalue;
		}
	}

	// 范围攻击伤害衰减（单体攻击默认1）
	hpdec *= atkdata.damping;

	// 吸收伤害
	hpdec = manageShieldLife(hpdec);

	return hpdec;
}

float LivingAttrib::getWeaponSkinAddition(float damage, ClientPlayer* atkplayer)
{

	if (CityConfig::getSingletonPtr() && CityConfig::getSingletonPtr()->getOtherConfig().m_weaponSkinAddition == false)
	{
		return damage;//配置mod屏蔽皮肤伤害加成
	}

	// 攻击方是玩家 - 武器皮肤加成/增加伤害
	if (atkplayer != nullptr)
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			//皮肤加成1 - 增加X%的伤害
			const char* attrType1 = "WeaponSkin_System_AttackDamage";
			float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType1, atkplayer);

			//皮肤加成2 - X%几率造成伤害加X
			const char* attrType2 = "WeaponSkin_System_CapableDamage";
			float fvalue = weaponSkinMgr->GetSkinAdditionValue(attrType2, atkplayer);

			damage += damage * fpercent + fvalue;
		}
	}

	ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	if (thisplayer != nullptr) //承伤方是玩家 - 皮肤加成/伤害减免
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			const char* attrType = "WeaponSkin_System_ReduceDamage";
			float fpercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, thisplayer);
			damage = damage * (1.0f - fpercent);
		}

	}

	return damage;
}

bool LivingAttrib::handleDefanceStateDamaged(float& damage, ClientActor* attacker, OneAttackData& atkdata)
{
	/*
	* 防御状态下，根据防御武器的技能配置数据（防御范围和减伤百分比），对伤害做处理
	* 1、计算是否在防御范围
	* 2、减伤百分比
	* 3、削韧处理
	* 4、暂时默认针对玩家处理，以后可以针对类似野人等生物另作处理
	*/
	bool successDeface = false;
	if (!attacker && !atkdata.directAttacker) return successDeface;
	
	ActorLiving* pLiving = dynamic_cast<ActorLiving*>(m_OwnerActor);
	if (pLiving && pLiving->IsInDefanceState())
	{
		BackPackGrid* grid = getEquipGrid(EQUIP_WEAPON);
		if (grid)
		{
			// 防御扣耐久
			if (grid->def)
			{
				const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(grid->def->ID);
				if (toolDef != NULL && toolDef->Duration > 0) {
					damageEquipItemWithType(EQUIP_WEAPON, toolDef->AtkDuration);
				}
			}
		}

		float defanceRange = 0.f;
		float damageReduce = 1.0f;
		float knockbackResist = 0.f;
		
		int currtoolId = 0;
		
		ClientMob* thisMob = dynamic_cast<ClientMob*>(m_OwnerActor);
		ClientPlayer* thisPlayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		if (thisMob != nullptr)
			currtoolId = thisMob->getCurToolID();
		else if (thisPlayer != nullptr)
			currtoolId = thisPlayer->getCurToolID();
		//if (currtoolId == 0) return damage;
		
		// 获取手持道具防御范围和减伤百分比
		const ItemDef* def = GetDefManagerProxy()->getItemDef(currtoolId);
		if (def)
		{
			for (int i = 0; i < (int)def->SkillID.size(); i++)
			{
				const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
				if (skilldef)
				{
					for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
					{
						ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
						// 防御减伤数值
						if (functiondef->oper_id == 20)
						{
							defanceRange = functiondef->func.defanceDamgeReduceFun.defanceRange;
							damageReduce = functiondef->func.defanceDamgeReduceFun.damageReduce;
							knockbackResist = functiondef->func.defanceDamgeReduceFun.knockbackResist;
						}
					}

					// 击退抗性
					if (atkdata.knockback > 0)
					{
						atkdata.knockback -= knockbackResist;
						if (atkdata.knockback < 0)
							atkdata.knockback = 0;
					}

					// 玩家消耗体力
					auto playerAttrib = thisPlayer ? thisPlayer->getPlayerAttrib() : nullptr;
					if (playerAttrib)
					{
						for (int j = 0; j < (int)skilldef->ItemSkillCosts.size(); j++)
						{
							// 体力消耗
							if (skilldef->ItemSkillCosts[j].CostType == 1 || skilldef->ItemSkillCosts[j].CostType == 3)
							{
								// 若都不显示，则体力值饥饿值都不更新
								if (playerAttrib->strengthFoodShowState() != SFS_Empty)
								{
									// 旧版饥饿度
									if (!playerAttrib->useCompatibleStrength())
									{
										playerAttrib->m_FoodLevel -= skilldef->ItemSkillCosts[j].CostVal;
										if (playerAttrib->m_FoodLevel < 0.f)
										{
											playerAttrib->m_FoodLevel = 0.f;
										}

									}
									// 新版体力值
									else
									{
										playerAttrib->addStrength((float)-skilldef->ItemSkillCosts[j].CostVal);
									}
								}
							}
						}
					}
				}
			}
		}

		/*
		* 判断攻击是否在防御范围
		*/
		float playerYaw = pLiving->getLocoMotion()->m_RotateYaw;
		float defanceRangeMin = playerYaw - defanceRange;
		float defanceRangeMax = playerYaw + defanceRange;

		WCoord playerPos = pLiving->getPosition();
		WCoord attackerPos = atkdata.directAttacker ? atkdata.directAttacker->getPosition() : attacker->getPosition();
		float attackerYaw = 0.f, attackerPitch = 0.f;
		Rainbow::Vector3f tempDir = (attackerPos - playerPos).toVector3();
		Direction2PitchYaw(&attackerYaw, &attackerPitch, tempDir);

		/*
		* 在防御范围内
		* 防御者朝向在defanceRange - 180 到 180 - defanceRange之间，直接用防御范围区间判断，>= rangeMin && <= rangeMax 就是在范围内
		* 防御者朝向小于 defanceRange - 180 ，<= rangeMin || >= rangeMax 就是在范围内
		* 防御者朝向大于 180 - defanceRange ，<= rangeMax - 360 || >= rangeMin 就是在范围内
		*/
		int limitMin = defanceRange - 180;
		int limitMax = 180 - defanceRange;
		if ((playerYaw >= limitMin && playerYaw <= limitMax && attackerYaw >= defanceRangeMin && attackerYaw <= defanceRangeMax)
			|| (playerYaw < limitMin && (attackerYaw >= (defanceRangeMin + 360) || attackerYaw <= defanceRangeMax))
			|| (playerYaw > limitMax && (attackerYaw >= defanceRangeMin || attackerYaw <= (defanceRangeMax - 360))))
		{
			successDeface = true;
			damage = damage * damageReduce;

			// 播放防御特效
			if (thisPlayer != nullptr)
			{
				if (currtoolId == 12309)
				{
					thisPlayer->playWeaponMotion("dunfan", true, 0, 2.0f);
				}
				else
				{
					// 双持播左边
					if (isDoubleWeapon(currtoolId))
						thisPlayer->playWeaponMotion("dunfan", true, 0, 1.f, true, 1);
					else
						thisPlayer->playWeaponMotion("dunfan");
				}
			}
			else
			{
				// 双持播左边
				if (isDoubleWeapon(currtoolId))
					pLiving->getBody()->playWeaponMotion("dunfan", true, 0, 1.f, 1);
				else
					pLiving->getBody()->playWeaponMotion("dunfan");
			}

			// 播放防御成功音效
			auto sound = m_OwnerActor->getSoundComponent();
			if (sound && def)
			{
				sound->playSound("newAtkSys.defense_succeed", 1.0f, 1.0f);
			}
		}
		// 在防御范围外，解除防御姿态
		else
		{
			pLiving->setInDefanceState(false);
		}

		// 老的逻辑移植到这里
		WCoord dp;
		if (attacker != nullptr) {
			dp = m_OwnerActor->getLocoMotion()->getPosition() - attacker->getLocoMotion()->getPosition();
		}

		Rainbow::Vector3f kickVector = dp.toVector3();
		kickVector.Truncate(10.0f);
		m_OwnerActor->setMotionChange(kickVector, true);
	}

	return successDeface;
}

void LivingAttrib::handleToughnessDeduction(ClientActor* attacker, OneAttackData& atkdata)
{
	ClientPlayer* thisPlayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	ClientMob* thisMob = dynamic_cast<ClientMob*>(m_OwnerActor);

	int touReduce = 0;
	// 技能削韧
	if (atkdata.touReduce > 0)
	{
		touReduce = atkdata.touReduce;
	}
	// 普攻削韧
	else if (atkdata.touReduce == 0)
	{
		ClientMob* attackMob = dynamic_cast<ClientMob*>(attacker);
		ClientPlayer* attackPlayer = dynamic_cast<ClientPlayer*>(attacker);
		ClientActorProjectile* attackProj = dynamic_cast<ClientActorProjectile*>(atkdata.directAttacker);

		// 攻击者是投掷物
		if (attackProj)
		{
			if (attackProj->m_touReduce > 0)
			{
				touReduce = attackProj->m_touReduce;
			}
		}
		// 攻击者是玩家
		else if (attackPlayer)
		{
			// 玩家攻击削韧保底5（策划：cjt）
			touReduce = 5;
			const ToolDef* def = GetDefManagerProxy()->getToolDef(attackPlayer->getCurToolID());
			if (def && def->TouReduce > 0)
			{
				touReduce = def->TouReduce;
			}
		}
		// 攻击者是怪物
		else if (attackMob)
		{
			/*
			* 怪物读monster表的削刃值，怪物有工具的话monster的削刃值+tool的削刃值
			* 保底不填都是5
			* 策划：cjt
			*/
			touReduce = 5;
			const MonsterDef* mdef = GetDefManagerProxy()->getMonsterDef(attackMob->getMonsterId());
			if (mdef && mdef->TouReduce > 0)
			{
				touReduce = mdef->TouReduce;
			}
			const ToolDef* def = GetDefManagerProxy()->getToolDef(attackMob->getCurToolID());
			if (def && def->TouReduce > 0)
			{
				touReduce += def->TouReduce;
			}
		}
	}

	// 玩家
	if (thisPlayer && thisPlayer->IsInDefanceState())
	{
		// 削韧处理
		BackPackGrid* grid = getEquipGrid(EQUIP_WEAPON);
		if (grid)
		{
			if (touReduce > 0)
			{
				grid->addToughness(-touReduce);
				addToughnessTotal(-touReduce);
			}

			// 破韧（手持装备）
			if (grid->getToughness() <= 0)
			{
				// 使用CD
				const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(thisPlayer->getCurToolID());
				if (tooldef)
				{
					thisPlayer->setSkillCD(tooldef->ID, tooldef->SkillCD);
					thisPlayer->syncSkillCD(tooldef->ID, tooldef->SkillCD);
				}

				thisPlayer->setInDefanceState(false);

				// 播放破盾特效
				if (isDoubleWeapon(thisPlayer->getCurToolID()))
					thisPlayer->playWeaponMotion("podun", true, 0, 1.f, true, 1);
				else
					thisPlayer->playWeaponMotion("podun");

				// 播放破韧音效
				auto sound = m_OwnerActor->getSoundComponent();
				if (sound)
				{
					sound->playSound("newAtkSys.defense_break", 1.0f, 1.0f);
				}
			}

			// 破韧（防御者）
			if (getToughnessTotal() <= 0)
			{
				// 破韧动作
				thisPlayer->getBody()->setCurAnim(-1, 1);
				thisPlayer->playAnim(SEQ_BROKEN_TOUGHNESS);

				// 破韧buff
				thisPlayer->getPlayerAttrib()->addBuff(TOUGHNESSBREAK_PLAYER_BUFF, 1);
			}
		}
	}
	else if(thisMob)
	{
		if (getToughnessTotal() > 0)
		{
			addToughnessTotal(-touReduce);

			// 破韧（防御者）
			if (getToughnessTotal() <= 0)
			{
				// 破韧buff
				thisMob->getMobAttrib()->addBuff(TOUGHNESSBREAK_MONSTER_BUFF, 1);

				// 取消防御
				thisMob->setInDefanceState(false);
			}
		}
	}
}

void LivingAttrib::handleRepelResist(float& knockback)
{
	/*
	* 击退抵抗
	* 怪物：装备叠加、自身配置
	* 玩家：装备叠加
	*/
	ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	ClientMob* thismob = dynamic_cast<ClientMob*>(m_OwnerActor);

	int repelRes = 0;
	if (thisplayer || thismob)
	{
		for (int i = 0; i < EQUIP_WEAPON; i++)
		{
			BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (grid && grid->def)
			{
				const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
				if (tool)
				{
					repelRes += tool->RepelRes;
				}
			}
			//- 击退抗性：数值，减少被击退的概率 renjie
			repelRes += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, ENCHANT_REPEL_RES);
		}
	}
	if (thismob)
	{
		const MonsterDef* mdef = GetDefManagerProxy()->getMonsterDef(thismob->getMonsterId());
		if (mdef != nullptr) repelRes += mdef->RepelRes;
	}
	// 击退抵抗
	if (GenRandomInt(10000) < repelRes)
	{
		knockback = 0;
	}

}

float LivingAttrib::getKnockback(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype)
{
	float knock = 1.0f;
	knock += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_KNOCK, knock);

	int val = getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_KNOCK, atktype, targettype) + 0.5;
	knock += val;
	if (knock > 30.0f) { knock = 30.0f; }

	return knock;
}


float LivingAttrib::getKnockUp(ATTACK_TYPE atktype, ATTACK_TARGET_TYPE targettype)
{
	float knock = 0;
	float knockUpPer = 1;
	//- 击飞：高度，数值，概率(新增)，百分比 renjie 击飞需要判断概率
	knock += int(getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_KNOCKUP, atktype, targettype, &knockUpPer) + 0.5 );
	if (GenRandomInt(0,100)> (knockUpPer*100))
	{
		knock = 0;
	}
	knock += getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_ATTACK_FLY, knock);

	return knock;
}

float LivingAttrib::getKnockbackResistance()
{
	float resist = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_KNOCK_RESIST, 1.0f);
	//if(isNewStatus())
	//	resist = getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_KNOCK_RESIST, 1.0f);
	//else
	//	resist = getModAttrib(MODATTR_KNOCK_RESIST);

	for(int i=0; i<EQUIP_WEAPON; i++)
	{
		resist += getEquipEnchantValue((EQUIP_SLOT_TYPE)i, ENCHANT_KNOCK_RESIST, ATTACK_ALL, ATTACK_TARGET_ALL);
	}

	return resist;
}

int LivingAttrib::getFireAspect(int &bufflv)
{
	float t2;
	float t1= getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_TARGET_FIRE, ATTACK_ALL, ATTACK_TARGET_ALL, &t2);

	float ct2 = t2 + 0.5f;
	bufflv = ct2;
	return int(t1);
}

int LivingAttrib::getBleedAspect(int& bufflv)
{
	float t2;
	float t1 = getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_TARGET_BLEED, ATTACK_ALL, ATTACK_TARGET_ALL, &t2);

	float ct2 = t2 + 0.5f;
	bufflv = ct2;
	return int(t1);
}

int LivingAttrib::getIceAspect(int& bufflv)
{
	float t2;
	float t1 = getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_TARGET_ICE, ATTACK_ICE, ATTACK_TARGET_ALL, &t2);

	float ct2 = t2 + 0.5f;
	bufflv = ct2;
	return int(t1);
}

float LivingAttrib::getSpeedInAir()
{
	return VELOCITY_INAIR;
}

float LivingAttrib::getFlySpeed()
{
	return 5.0f;
}

float LivingAttrib::getAttackAndDefenseBase(int type, bool bAttack)
{
	if (bAttack)
		return m_fBaseAttack[type == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
	
	return m_fBaseArmor[type == ATTACK_TYPE::ATTACK_PUNCH ? 0 : 1];
}

//可以把老的buff逻辑屏蔽了
bool LivingAttrib::isNewStatus()
{
	bool isNew = true;

	return isNew;
}

bool LivingAttrib::hasStatusEffect(int iEffectId)
{
	//if (!isNewStatus()) { return false; }

	for (size_t i = 0; i < m_Buffs.size();i++) {
		if (m_Buffs[i].def) {
			for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {
				if (m_Buffs[i].def->Status.EffInfo[j].CopyID == iEffectId) {
					return true;
				}
				else if (m_Buffs[i].def->Status.EffInfo[j].CopyID == 0) {
					break;
				}
			}
		}
	}

	return false;
}

//1 死亡是否清除（只有玩家才会有）；2 主动攻击是否清除； 3受击是否清除
void LivingAttrib::checkAllStatusClearFlags(int clearType, int exceptId /* = 0 */)
{
	//DeathClear AttackClear DamageClear : 1清除，2不清除
	int buffids[64];
	int count = 0;

	for (size_t i = 0; i < m_Buffs.size(); i++) {
		const BuffDef* def = m_Buffs[i].def;
		if (!def) { continue; }
		if (clearType == 2) {
			if (def->Status.AttackClear == 1) {
				buffids[count++] = m_Buffs[i].buffid;
			}
		}
		else if (clearType == 3) {
			if (def->Status.DamageClear == 1 && (exceptId == 0 || exceptId != m_Buffs[i].buffid)) {
				buffids[count++] = m_Buffs[i].buffid;
			}
		}
	}

	for (int i = 0; i < count; i++) removeBuff(buffids[i], false);
}

/***********************************************************************************
函数名: getAttackPointByStatusAttType
功  能: 获取加上status效果后的伤害点数
参  数:	atktype: 伤害类型
***********************************************************************************/
float LivingAttrib::getHurtPointWithStatus(ATTACK_TYPE atktype, float baseHurtValue, ClientActor* attacker/* = NULL*/)
{
	//return baseHurtValue;

	ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	ClientMob* pMob = dynamic_cast<ClientMob*>(m_OwnerActor);

	if (atktype == ATTACK_PUNCH || atktype == ATTACK_RANGE || atktype == ATTACK_EXPLODE || atktype == ATTACK_FIRE ||
		atktype == ATTACK_POISON ||
		atktype == ATTACK_WITHER ||
		atktype == ATTACK_ICE)
	{
		//----------------------------------------攻击者----------------------------------------
		if (attacker)
		{
			LivingAttrib* pAttrib = dynamic_cast<LivingAttrib*>(attacker->getAttrib());

			if (pAttrib)
			{
				static auto getAtkType = [this](int type) -> int {
					if (type == ATTACK_PUNCH)
						return BUFFATTRT_MELEE_DAMAGE;
					else if (type == ATTACK_RANGE)
						return BUFFATTRT_REMOTE_DAMAGE;
					else if (type == ATTACK_EXPLODE)
						return BUFFATTRT_EXPLODE_DAMAGE;
					else if (type == ATTACK_FIRE)
						return BUFFATTRT_FIRE_DAMAGE;
					else if (type == ATTACK_POISON)
						return BUFFATTRT_POISON_DAMAGE;
					else if (type == ATTACK_WITHER)
						return BUFFATTRT_CONFUSION_DAMAGE;
					else if (type == ATTACK_ICE)
						return BUFFATTRT_ICE_HARM;
					return type;
				};
				//1.攻击伤害
				int iAttType = getAtkType(atktype);
				baseHurtValue += pAttrib->getActorAttValueWithStatus(iAttType, baseHurtValue);

				//2.所有伤害
				baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_ALL_DAMAGE, baseHurtValue);

				if (thisplayer)
					baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_PLAYER_DAMAGE, baseHurtValue);			//3.对玩家的伤害
				else
				{
					if (pMob)
					{
						if (pMob->m_Def->Nature == ATTACK_TARGET_ANIMAL)
							baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_ACTOR_DAMAGE, baseHurtValue);	//4.对动物的伤害
						else if (pMob->m_Def->Nature == ATTACK_TARGET_SAVAGE)
							baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_SAVAGE_DAMAGE, baseHurtValue);	//5.对野人的伤害
						else if (pMob->m_Def->Nature == ATTACK_TARGET_UNDEAD)
							baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_MONSTER_DAMAGE, baseHurtValue);	//6.对魔物的伤害
						else if (pMob->m_Def->Nature == ATTACK_TARGET_ZOMBIE)
							baseHurtValue += pAttrib->getActorAttValueWithStatus(BUFFATTRT_ZOMBIE_DAMAGE, baseHurtValue);	//7.对僵尸的伤害
					}
				}
			}
		}

		//----------------------------------------受击者----------------------------------------
		static auto getHurtType = [this](int type) -> int {
			if (type == ATTACK_PUNCH)
				return BUFFATTRT_MELEE_HURT;
			else if (type == ATTACK_RANGE)
				return BUFFATTRT_REMOTE_HURT;
			else if (type == ATTACK_EXPLODE)
				return BUFFATTRT_EXPLODE_HURT;
			else if (type == ATTACK_FIRE)
				return BUFFATTRT_FIRE_HURT;
			else if (type == ATTACK_POISON)
				return BUFFATTRT_POISON_HURT;
			else if (type == ATTACK_WITHER)
				return BUFFATTRT_CONFUSION_HURT;
			else if (type == ATTACK_ICE)
				return BUFFATTRT_ICE_TEMPERATURE;
			return type;
		};
		//1.受击伤害
		int iAttType = getHurtType(atktype);
		baseHurtValue += getActorAttValueWithStatus(iAttType, baseHurtValue);

		//2.所有受击伤害
		baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_ALL_HURT, baseHurtValue);

		if(thisplayer)
			baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_PLAYER_DAMAGE, baseHurtValue);								//3.玩家受击伤害
		else
		{
			if (pMob)
			{
				if (pMob->m_Def->Nature == ATTACK_TARGET_ANIMAL)
					baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_ACTOR_DAMAGE, baseHurtValue);		//4.动物的受击伤害
				else if (pMob->m_Def->Nature == ATTACK_TARGET_SAVAGE)
					baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_SAVAGE_DAMAGE, baseHurtValue);	//5.野人的受击伤害
				else if (pMob->m_Def->Nature == ATTACK_TARGET_UNDEAD)
					baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_MONSTER_DAMAGE, baseHurtValue);	//6.魔物的受击伤害
				else if (pMob->m_Def->Nature == ATTACK_TARGET_ZOMBIE)
					baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_ZOMBIE_DAMAGE, baseHurtValue);	//7.僵尸的受击伤害
			}
		}
	}
	else if (atktype == ATTACK_FALLING)
	{
		//跌落受击伤害
		baseHurtValue += getActorAttValueWithStatus(BUFFATTRT_FALL_HURT, baseHurtValue);
	}

	//固定受击伤害
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_FIXED_HURT, vValue);
	if (vValue.size() > 0)
		baseHurtValue = getActorAttValueWithStatus(BUFFATTRT_FIXED_HURT, 0);

	if (baseHurtValue < 0)
		baseHurtValue = 0;

	return baseHurtValue;
}

float LivingAttrib::getTargetDamageIns(ClientActor* attacker, bool isPer)
{
	float damageIns = 0.f;

	// 攻击者对特定类型受击者伤害加成
	if (attacker)
	{
		ClientPlayer* thisplayer = dynamic_cast<ClientPlayer*>(m_OwnerActor);
		ClientMob* thisMob = dynamic_cast<ClientMob*>(m_OwnerActor);
		ActorBoss* Boss = dynamic_cast<ActorBoss*>(m_OwnerActor);//boss不是clientMob 增加boss类型判断
		LivingAttrib* atkAttrib = dynamic_cast<LivingAttrib*>(attacker->getAttrib());
		int MonsterNature = ATTACK_TARGET_ALL;
		if (thisMob && thisMob->m_Def)
		{
			MonsterNature = thisMob->m_Def->Nature;
		}
		else if (Boss && Boss->getDef())
		{
			MonsterNature = Boss->getDef()->Nature;
		}
		if (atkAttrib)
		{
			if (thisplayer)
				damageIns += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_PLAYER_DAMAGE, isPer);			// 对玩家的伤害
			else
			{
				if (thisMob || Boss)
				{
					if (MonsterNature == ATTACK_TARGET_ANIMAL)
						damageIns += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ACTOR_DAMAGE, isPer);	// 对动物的伤害
					else if (MonsterNature == ATTACK_TARGET_SAVAGE)
						damageIns += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_SAVAGE_DAMAGE, isPer);	// 对野人的伤害
					else if (MonsterNature == ATTACK_TARGET_UNDEAD)
						damageIns += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_MONSTER_DAMAGE, isPer);	// 对魔物的伤害
					else if (MonsterNature == ATTACK_TARGET_ZOMBIE)
						damageIns += atkAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ZOMBIE_DAMAGE, isPer);	// 对僵尸的伤害

					if (isPer)// 怪物类增伤附魔 renjie
					{
						damageIns += atkAttrib->getEnchantIncPer(ENCHANT_MONSTER_DAMAGE, ATTACK_ALL, ATTACK_TARGET_TYPE(MonsterNature));
					}
				}
			}
		}
	}
	return damageIns;
}

float LivingAttrib::getTargetDamageDec(ClientActor* attacker, bool isPer)
{
	float damageDec = 0.f;

	if (attacker)
	{
		// 受击者对特定类型攻击者受击减免
		ClientPlayer* atkplayer = dynamic_cast<ClientPlayer*>(attacker);
		ClientMob* atkMob = dynamic_cast<ClientMob*>(attacker);

		LivingAttrib* thisAttrib = dynamic_cast<LivingAttrib*>(m_OwnerActor->getAttrib());

		if (thisAttrib)
		{
			if (atkplayer)
			{
				damageDec += thisAttrib->getActorAttValueWithStatusByType(BUFFATTRT_PLAYER_HURT, isPer);			// 对玩家的受击伤害
			}
			else
			{
				if (atkMob)
				{
					if (atkMob->m_Def->Nature == ATTACK_TARGET_ANIMAL)
						damageDec += thisAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ACTOR_HURT, isPer);		// 对动物的受击伤害
					else if (atkMob->m_Def->Nature == ATTACK_TARGET_SAVAGE)
						damageDec += thisAttrib->getActorAttValueWithStatusByType(BUFFATTRT_SAVAGE_HURT, isPer);	// 对野人的受击伤害
					else if (atkMob->m_Def->Nature == ATTACK_TARGET_UNDEAD)
						damageDec += thisAttrib->getActorAttValueWithStatusByType(BUFFATTRT_MONSTER_HURT, isPer);	// 对魔物的受击伤害
					else if (atkMob->m_Def->Nature == ATTACK_TARGET_ZOMBIE)
					{
						damageDec += thisAttrib->getActorAttValueWithStatusByType(BUFFATTRT_ZOMBIE_HURT, isPer);	// 对僵尸的受击伤害

						float dec = thisAttrib->getModAttrib(MOBATTR_DAMAGED_ZOMBIE);
						if (dec > 0 && dec <= 1.0f)
						{
							dec = -dec;
							damageDec += dec;
						}
					}
				}
			}
		}
	}

	return damageDec;
}

/***********************************************************************************
函数名: getAttackPointWithStatus
功  能: 获取加上status效果后的攻击力点数
参  数:	atktype: 伤害类型
***********************************************************************************/
float LivingAttrib::getAttackPointWithStatus(ATTACK_TYPE atktype, float baseAttackPoint)
{
	//return baseAttackPoint;

	if (atktype >= ATTACK_PUNCH && atktype <= ATTACK_WITHER || atktype == ATTACK_ICE)
	{
		int iAttType = atktype + BUFFATTRT_MELEE_ATK;
		if (atktype == ATTACK_ICE)
		{
			iAttType = BUFFATTRT_ICE_ATK;
		}
		baseAttackPoint += getActorAttValueWithStatus(iAttType, baseAttackPoint);

		//所有攻击:BUFFATTRT_ALL_ATK
		baseAttackPoint += getActorAttValueWithStatus(BUFFATTRT_ALL_ATK, baseAttackPoint);
	}

	return baseAttackPoint;
}

/***********************************************************************************
函数名: getAromrPointWithStatus
功  能: 获取加上status效果后的防御点数
参  数:	atktype: 伤害类型
***********************************************************************************/
float LivingAttrib::getAromrPointWithStatus(ATTACK_TYPE atktype, float baseArmorPoint)
{
	//return baseArmorPoint;
	int iAttType = -1;
	int iAttType2 = -1;
	if (atktype == PHYSICS_ATTACK)
	{
		iAttType = BUFFATTRT_PHYSICS_DEF;
	}
	else if (atktype < MAX_PHYSICS_ATTACK)
	{
		if (atktype == ATTACK_PUNCH)
			iAttType = BUFFATTRT_MELEE_DEF;
		else if (atktype == ATTACK_RANGE)
			iAttType = BUFFATTRT_REMOTE_DEF;
		else if (atktype == ATTACK_EXPLODE)
			iAttType = BUFFATTRT_EXPLODE_DEF;

		if (iAttType != -1)
			iAttType2 = BUFFATTRT_PHYSICS_DEF;
	}
	else if (atktype == MAX_MAGIC_ATTACK)
	{
		iAttType = BUFFATTRT_MAGIC_DEF;
	}
	else
	{
		if (atktype == ATTACK_FIRE)
			iAttType = BUFFATTRT_FIRE_DEF;
		else if (atktype == ATTACK_POISON)
			iAttType = BUFFATTRT_POISON_DEF;
		else if (atktype == ATTACK_WITHER)
			iAttType = BUFFATTRT_CONFUSION_DEF;
		else if (atktype == ATTACK_ICE)
			iAttType = BUFFATTRT_ICE_BRUISE;

		if (iAttType != -1)
			iAttType2 = BUFFATTRT_MAGIC_DEF;
	}

	if (iAttType != -1 || iAttType2 != -1)
	{
		if(iAttType != -1)
			baseArmorPoint += getActorAttValueWithStatus(iAttType, baseArmorPoint);

		if (iAttType2 != -1)
			baseArmorPoint += getActorAttValueWithStatus(iAttType2, baseArmorPoint);

		//所有防御:BUFFATTRT_ALL_DEF
		baseArmorPoint += getActorAttValueWithStatus(BUFFATTRT_ALL_DEF, baseArmorPoint);
	}
	return baseArmorPoint;
}

float LivingAttrib::getAttackPointWithStatusSingle(ATTACK_TYPE atktype, float baseAttackPoint)
{
	if (atktype >= ATTACK_PUNCH && atktype <= ATTACK_WITHER || atktype == ATTACK_ICE)
	{
		int iAttType = atktype + BUFFATTRT_MELEE_ATK;
		if (atktype == ATTACK_ICE)
		{
			iAttType = BUFFATTRT_ICE_ATK;
		}
		baseAttackPoint += getActorAttValueWithStatus(iAttType, baseAttackPoint);
	}

	return baseAttackPoint;
}

float LivingAttrib::getAromrPointWithStatusSingle(ATTACK_TYPE atktype, float baseArmorPoint)
{
	if (atktype >= ATTACK_PUNCH && atktype <= ATTACK_WITHER || atktype == ATTACK_ICE)
	{
		int iAttType = atktype + BUFFATTRT_MELEE_DEF;
		if (atktype == ATTACK_ICE)
		{
			iAttType = BUFFATTRT_ICE_BRUISE;
		}
		baseArmorPoint += getActorAttValueWithStatus(iAttType, baseArmorPoint);
	}

	return baseArmorPoint;
}

//该接口只适用于改变单个属性的情况
float LivingAttrib::getActorAttValueWithStatus(int iAttType, float baseValue)
{
	if (/*!isNewStatus() || */iAttType <= 0) { return 0.0f; }
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(iAttType, vValue);

	float fBuffValue = 0.0f;
	for (int i = 0; i < (int)vValue.size(); i++) {
		if (vValue[i].vValue.size() > 0) {
			if (vValue[i].vValue[0].iType == 1) {
				fBuffValue += baseValue*vValue[i].vValue[0].value / 100.0f;
			}
			else {
				if (vValue[i].vValue[0].iType == 3)
					fBuffValue += vValue[i].vValue[0].value / 10.0f;
				else
					fBuffValue += vValue[i].vValue[0].value;
			}
		}
	}

	return fBuffValue;
}

float LivingAttrib::getActorAttValueWithStatusByType(int iAttType, bool isPer)
{
	if (/*!isNewStatus() || */iAttType <= 0) { return 0.0f; }
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(iAttType, vValue);

	float fBuffValue = 0.0f;
	for (int i = 0; i < (int)vValue.size(); i++) {
		if (vValue[i].vValue.size() > 0) {
			if (isPer)
			{
				if (vValue[i].vValue[0].iType == 1) {
					fBuffValue += vValue[i].vValue[0].value / 100.0f;
				}
			}
			else 
			{
				if (vValue[i].vValue[0].iType == 3)
					fBuffValue += vValue[i].vValue[0].value / 10.0f;
				else if(vValue[i].vValue[0].iType == 2)
					fBuffValue += vValue[i].vValue[0].value;
			}
		}
	}

	return fBuffValue;
}

float LivingAttrib::getRandomAttValueWithStatus(int iAttType)
{
	if (/*!isNewStatus() || */iAttType <= 0) { return 0.0f; }
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(iAttType, vValue);

	float fBuffValue = 0.0f;
	for (int i = 0; i < (int)vValue.size(); i++) {
		if (vValue[i].vValue.size() > 1) {
			if (vValue[i].vValue[0].iType == 1) {
				int iRandNum = 1 + GenRandomInt(100);
				if (iRandNum <= vValue[i].vValue[0].value) {//命中
					fBuffValue += vValue[i].vValue[1].value;
				}
			}
		}
	}

	return fBuffValue;
}

void LivingAttrib::getStatusAddAttInfo(int iAttType, std::vector<StatusAttInfo>& vValue)
{
	vValue.clear();
	auto it = m_StatusAddAttInfo.find(iAttType);
	if (it != m_StatusAddAttInfo.end()) {
		vValue = it->second;
	}
}

void LivingAttrib::addStatusEffects(ActorBuff* status)
{
	if (!status) { return; }

	CustomScriptEffectArr custEffArr;
	for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
		addOneEffect(status, status->def->Status.EffInfo[i], &custEffArr);
	}
	if (!custEffArr.arrCustomScriptEffect.empty())
	{
		// 打包通知到lua
		addCustomEffect(status, custEffArr, nullptr);
	}
}

void LivingAttrib::addOneEffect(ActorBuff* status, const EffectInfo& effect, CustomScriptEffectArr* pCustEffArr)
{
	if (effect.CopyID <= 0) { return; }

	auto effDef = GetDefManagerProxy()->getBuffEffectDef(effect.CopyID);
	if (!effDef) { return; }

	if (effDef->EditType == 1) //EditType 这个字段代表插件编辑里面的效果库分类 为啥要这个判断
	{
		addNormalEffect(status, effect, effDef);
		BuffAttrType type = getEffectType(effect, effDef);
		execNormalEffect(type);
	}
	else if (effDef->EditType == 2)
	{
		addSpecialEffect(status, effect, effDef);
	}
	else
	{
		//自定义的效果组件
		if (!pCustEffArr)
		{
			CustomScriptEffectArr arr;
			arr.arrCustomScriptEffect.push_back((jsonxx::Object*)&effect.customScriptEffect);
			addCustomEffect(status, arr, effDef);
		}
		else
		{
			pCustEffArr->arrCustomScriptEffect.push_back((jsonxx::Object*)&effect.customScriptEffect);
		}
	}
}

void LivingAttrib::addNormalEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def)
{
	std::vector<int> vType;
	vType.clear();

	int iType = def->AttType;
	int isSpecial = iType != 0;
	int iUIType = 0;
	for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
		if (effect.Value[i] == -1000000) {
			break;
		}
		else {
			iUIType = def->EffectParam[i].UIType / STATUS_UI_BASE_VAL;
			vType.push_back(iUIType);
			if (iType <= 0 && iUIType == 1) {
				auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef(effect.Value[i]);
				if (enumdef) {
					iType = enumdef->AttType;
				}
			}
		}
	}
	if (iType == BuffAttrType::BUFFATTRT_WALK_SPEED)
	{
		LivingAttrib* pAtt = dynamic_cast<LivingAttrib*>(this);  // 20210819：移动速度效果改成非player类型的生物也有效  codeby： keguanqiang
		if (!pAtt) { return; }
	}

	if (iType == BuffAttrType::BUFFATTRT_HUNGER_SPEED) {
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
	}
	else if (iType == BuffAttrType::BUFFATTRT_HUNGER_MAX) {
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
	}
	//添加新效果 code-by:曹泽港
	else if (iType == BuffAttrType::BUFFATTRT_FORBID_EAT)
	{
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
		pAtt->setEnableEatFood(false);
    } else if (iType == BuffAttrType::BUFFATTRT_IMMUNE_BAD_EFFECT) {
        const BuffDef *buffDef = status->def;
        if (buffDef != nullptr) {
            for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
                if (buffDef->Status.EffInfo[i].CopyID == effect.CopyID) {
                    for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {
                        int value = buffDef->Status.EffInfo[i].Value[j];
                        if (value > 0) {
                            AddImmueBuff(value);
                        }
                    }
                }
            }
        }
    } else if (iType == BuffAttrType::BUFFATTRT_IMMUNE_NATURE_EFFECT) {
        const BuffDef *buffDef = status->def;
        if (buffDef != nullptr) {
            for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
                if (buffDef->Status.EffInfo[i].CopyID == effect.CopyID) {
                    for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {
                        int nature = buffDef->Status.EffInfo[i].Value[j];
                        if (nature > 0) {
                            std::vector<int> buffIdVect = GetDefManagerProxy()->GetBuffByNature(nature);
                            for (auto buffId : buffIdVect) {
                                AddImmueBuff(buffId);
                            }
                        }
                    }
                }
            }
        }
    }

	if (iType > 0) {
		StatusAttInfo stAttInfo;
		stAttInfo.iStatusId = status->buffid;
		stAttInfo.iStatusLv = status->bufflv;
		stAttInfo.iEffectId = effect.CopyID;
		stAttInfo.iStatusIdx = status->buffidx;

		auto it = m_StatusAddAttInfo.find(iType);
		if (it == m_StatusAddAttInfo.end()) {
			std::vector<StatusAttInfo> vValue;
			vValue.clear();

			for (int i = 0; i < (int)vType.size(); i++) {
				if (vType[i] == StatusUIType::Type_Enum && i == 0 && !isSpecial) { continue; }

				StatusValue stValue;
				if (vType[i] == StatusUIType::Type_Slider) {
					auto sliderdef = GetDefManagerProxy()->getBuffEffectSliderDef(def->EffectParam[i].UIType);
					stValue.iType = sliderdef ? sliderdef->NumericalType : 0;
				}
				else
					stValue.iType = 0;
				stValue.iParaType = def->EffectParam[i].UIType;
				stValue.value = (float)(effect.Value[i]);
				stAttInfo.vValue.push_back(stValue);
			}
			vValue.push_back(stAttInfo);
			m_StatusAddAttInfo[iType] = vValue;
		}
		else {
			for (int i = 0; i < (int)vType.size(); i++) {
				if (vType[i] == StatusUIType::Type_Enum && i == 0 && !isSpecial) { continue; }

				StatusValue stValue;
				if (vType[i] == StatusUIType::Type_Slider) {
					auto sliderdef = GetDefManagerProxy()->getBuffEffectSliderDef(def->EffectParam[i].UIType);
					stValue.iType = sliderdef ? sliderdef->NumericalType : 0;
				}
				else
					stValue.iType = 0;
				stValue.iParaType = def->EffectParam[i].UIType;
				stValue.value = (float)(effect.Value[i]);
				stAttInfo.vValue.push_back(stValue);
			}

			it->second.push_back(stAttInfo);
		}

		if (iType == BuffAttrType::BUFFATTRT_HUNGER_SPEED) {
			PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
			if (!pAtt) { return; }
		}
		
		//@TODO：多个同类型会重复调用
		if (iType == BUFFATTRT_STRENGTH)
		{
			updateStrength();
		}
		if (iType == BUFFATTRT_OVERFLOW_STRENGTH)
		{
			updateOverflowStrength();
		}
		if (m_OwnerActor == g_pPlayerCtrl && 
			(iType == BUFFATTRT_HP_MAX 
				|| iType == BUFFATTRT_HUNGER_MAX)
			)
		{
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
		}
	}
}

void LivingAttrib::addSpecialEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def)
{
	if (effect.CopyID <= 0 || def == NULL) return;

	addNormalEffect(status, effect, def);
	BuffAttrType type = getEffectType(effect, def);
	execSpecialEffect(type);
}

void LivingAttrib::addCustomEffect(ActorBuff* status, const CustomScriptEffectArr& custEffectArr, const BuffEffectDef* def)
{
	if (!status || !m_OwnerActor->getWorld())
		return;

	ScriptComponent* pScriptComponent = m_OwnerActor->getScriptComponent();
	if (!pScriptComponent)
	{
		pScriptComponent = m_OwnerActor->CreateComponent<ScriptComponent>("ScriptComponent");
	}

	long long idx = status->buffidx;
	long long lInstanceId = (idx << 32) + GetDefManagerProxy()->getRealStatusId(status->buffid, status->bufflv);
	pScriptComponent->OnEvent((int)CE_OnStatusAdd, false, lInstanceId, (void*)&custEffectArr, "CustomScriptEffectArr");
}

void LivingAttrib::removeStatusEffects(ActorBuff* status)
{
	for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
		removeOneEffect(status, status->def->Status.EffInfo[i]);
	}
}

void LivingAttrib::removeOneEffect(ActorBuff* status, const EffectInfo& effect)
{
	if (effect.CopyID <= 0) { return; }

	auto effDef = GetDefManagerProxy()->getBuffEffectDef(effect.CopyID);
	if (!effDef) { return; }
	
	if (effDef->EditType == 1) {
		removeNormalEffect(status, effect, effDef);
		BuffAttrType type = getEffectType(effect, effDef);
		execNormalEffect(type);
	}
	else if (effDef->EditType == 2)
	{
		removeSpecialEffect(status, effect, effDef);
	}
	else
	{
		removeCustomEffect(status, effect, effDef);
	}
}

void LivingAttrib::removeNormalEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def)
{
	int iType = def->AttType;
	int iUIType = 0;
	for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
		if (effect.Value[i] == -1000000) {
			break;
		}
		else {
			iUIType = def->EffectParam[i].UIType / STATUS_UI_BASE_VAL;
			if (iType <= 0 && iUIType == 1) {
				auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef(effect.Value[i]);
				if (enumdef) {
					iType = enumdef->AttType;
				}
			}
		}
	}

	auto it = m_StatusAddAttInfo.find(iType);
	if (it != m_StatusAddAttInfo.end()) {
		for (int i = it->second.size()-1; i >= 0; i--) {
			if (it->second[i].iStatusId == status->buffid && it->second[i].iStatusIdx == status->buffidx && it->second[i].iEffectId == effect.CopyID) {
				it->second.erase(it->second.begin() + i);
			}
		}

		if (it->second.size() == 0) {
			m_StatusAddAttInfo.erase(it);
		}
	}

	//恢复上限值
	if (iType == BuffAttrType::BUFFATTRT_HUNGER_MAX) {
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
		pAtt->setFoodLevel(pAtt->getFoodLevel());
	}
	else if (iType == BuffAttrType::BUFFATTRT_HUNGER_SPEED) {
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
	}
	else if (iType == BuffAttrType::BUFFATTRT_WALK_SPEED)
	{
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
	}
	//添加新效果 code-by:曹泽港
	else if (iType == BuffAttrType::BUFFATTRT_FORBID_EAT)
	{
		PlayerAttrib* pAtt = dynamic_cast<PlayerAttrib*>(this);
		if (!pAtt) { return; }
		pAtt->setEnableEatFood(true);
    } else if (iType == BuffAttrType::BUFFATTRT_IMMUNE_BAD_EFFECT) {
        const BuffDef *buffDef = status->def;
        if (buffDef != nullptr) {
            for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
                if (buffDef->Status.EffInfo[i].CopyID == effect.CopyID) {
                    for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {
                        int value = buffDef->Status.EffInfo[i].Value[j];
                        if (value > 0) {
                            RemoveImmueBuff(value);
                        }
                    }
                }
            }

        }
    } else if (iType == BuffAttrType::BUFFATTRT_IMMUNE_NATURE_EFFECT) {
        const BuffDef *buffDef = status->def;
        if (buffDef != nullptr) {
            for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
                if (buffDef->Status.EffInfo[i].CopyID == effect.CopyID) {
                    for (int j = 0; j < MAX_BUFF_ATTRIBS; j++) {
                        int nature = buffDef->Status.EffInfo[i].Value[j];
                        if (nature > 0) {
                            std::vector<int> buffIdVect = GetDefManagerProxy()->GetBuffByNature(nature);
                            for (auto buffId : buffIdVect) {
                                RemoveImmueBuff(buffId);
                            }
                        }
                    }
                }
            }
        }
    }
	else if (iType == BuffAttrType::BUFFATTRT_CHANGE_MODEL_TEXTURE)
	{
		auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
		if (temperatureComponent)
		{
			temperatureComponent->ChangeModelTexture(status->buffid,false);
		}
	}
	else if (iType == BuffAttrType::BUFFATTRT_END_CHANGE_ATTRIBUTE)
	{
		//if (def->EffectParam[0].UIType == ParaType_Temperature)
		//{
			if (effect.Value[0] != -1000000)
			{
				setTemperature(effect.Value[0]);
			}
		//}
	}
	else if (iType == BuffAttrType::BUFFATTRT_END_PLAY_EFFECT)
	{
		if (def->EffectParam[0].UIType == ParaType_Option_Particles)
		{

			/*int time = effect.Value[1];
			if (time == -1000000)
			{
				time = def->EffectParam[1].Default;
			}*/
			//if (effect.Value[0] != -1000000 && time != -1000000
			if (effect.Value[0] != -1000000)
			{
				ParticleDef* def = GetDefManagerProxy()->getParticleDef(effect.Value[0]);
				if (def)
				{
					char path[512] = "";
					sprintf(path, "particles/%s.ent", def->EffectName.c_str());
					WCoord pos = m_OwnerActor->getPosition();
					m_OwnerActor->getWorld()->getEffectMgr()->playParticleEffectAsync(path, pos, 40);
					if (effect.Value[1] != -1000000)
					{
						auto def = GetDefManagerProxy()->getSoundDef(effect.Value[1]);
						auto sound = m_OwnerActor->getSoundComponent();
						if (sound && def)
						{
							sound->playSound(def->SoundPath.c_str(), 1.0f, 1.0f);
						}
					}
				}
			}
		}
	}
	else if (iType == BuffAttrType::BUFFATTRT_SCREEN_DECALS)
	{
		auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
		if (temperatureComponent)
		{
			temperatureComponent->SetScreenDecals(status->buffid, false);
		}
	
	}
	else if (iType == BuffAttrType::BUFFATTRT_TEMPERATURE_IMPACT_DURATION)
	{
		auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
		if (temperatureComponent)
		{
			temperatureComponent->RemoveTempImpactDuration(status->buffid);
		}
	}
	else if (iType == BuffAttrType::BUFFATTRT_HANG_MODEL)
	{
		auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
		if (temperatureComponent)
		{
			temperatureComponent->Unfreeze(status->buffid);
		}
	}
	else if (iType == BuffAttrType::BUFFATTRT_TEMPERATURED_CHANGE)
	{
		const BuffDef* buffDef = status->def;
		if (buffDef != nullptr) {
			for (int i = 0; i < MAX_BUFF_ATTRIBS; i++)
			{
				if (buffDef->Status.EffInfo[i].CopyID == effect.CopyID)
				{
					float value = GetSliderValue(def->EffectParam[0].UIType, (float)(buffDef->Status.EffInfo[i].Value[0] / 10));
					if (value != SLIDER_VALUE_ERROR)
					{
						if (def->EffectParam[1].UIType == ParaType_Switch && (int)(buffDef->Status.EffInfo[i].Value[1]) == 1)
						{
							addTemperatureBuffEffect(-value);
						}
						else {
							addTemperatureBuffDefend(-value);
						}
					}
				}
			}
		}
	}


	//@TODO：多个同类型会重复调用
	if (iType == BUFFATTRT_STRENGTH)
	{
		updateStrength();
	}
	if (iType == BUFFATTRT_OVERFLOW_STRENGTH)
	{
		updateOverflowStrength();
	}
	if (m_OwnerActor == g_pPlayerCtrl &&
		(iType == BUFFATTRT_HP_MAX
			|| iType == BUFFATTRT_HUNGER_MAX)
		)
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", sandboxContext);
	}
}

void LivingAttrib::removeSpecialEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def)
{
	if (effect.CopyID <= 0) return;

	removeNormalEffect(status, effect, def);

	BuffAttrType type =	getEffectType(effect, def);
	execSpecialEffect(type);
}

void LivingAttrib::removeCustomEffect(ActorBuff* status, const EffectInfo& effect, const BuffEffectDef* def)
{
	if (!status || effect.CopyID <= 0 || !m_OwnerActor->getWorld())
		return;

	ScriptComponent* pScriptComponent = m_OwnerActor->getScriptComponent();
	if (!pScriptComponent)
	{
		pScriptComponent = m_OwnerActor->CreateComponent<ScriptComponent>("ScriptComponent");
	}

	long long idx = status->buffidx;
	long long lInstanceId = (idx << 32) + GetDefManagerProxy()->getRealStatusId(status->buffid, status->bufflv);
	pScriptComponent->OnEvent((int)CE_OnStatusRemove, false, lInstanceId);
}

BuffAttrType LivingAttrib::getEffectType(const EffectInfo& effect, const BuffEffectDef* def)
{

	int iType = def->AttType;
	int iUIType = 0;
	for (int i = 0; i < MAX_BUFF_ATTRIBS; i++) {
		if (effect.Value[i] == -1000000) {
			break;
		}
		else {
			iUIType = def->EffectParam[i].UIType / STATUS_UI_BASE_VAL;
			if (iType <= 0 && iUIType == 1) {
				auto enumdef = GetDefManagerProxy()->getBuffEffectEnumDef(effect.Value[i]);
				if (enumdef) {
					iType = enumdef->AttType;
				}
			}
		}
	}
	return (BuffAttrType)iType;
}

void LivingAttrib::execEffect(BuffAttrType type, std::vector<StatusAttInfo>& vSAInfo)
{
	switch (type)
	{
		case BUFFATTRT_CONTINUED_CHANGE_TEMPERATURE:
		{
			auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
			if (temperatureComponent)
			{
				if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() >= 3)
				{
					if (vSAInfo[0].vValue[2].iParaType == ParaType_Switch)
					{
						bool isSuitableTemperature = true;
						if (vSAInfo[0].vValue[2].value == 1)
						{
							isSuitableTemperature = false;
						}
						float value = GetSliderValue(vSAInfo[0].vValue[0].iParaType, vSAInfo[0].vValue[0].value);
						if (value != SLIDER_VALUE_ERROR)
						{
							float value1 = GetSliderValue(vSAInfo[0].vValue[1].iParaType, vSAInfo[0].vValue[1].value / 10);
							if (value1 != SLIDER_VALUE_ERROR)
							{
								temperatureComponent->ChangeTemperature(value1, value, isSuitableTemperature);
							}
						}
					}
				}
				else
				{
					temperatureComponent->ChangeTemperature(0, 0);
				}
			}
			break;
		}
		case BUFFATTRT_TEMPERATURED_CHANGE:
		{
			if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() >= 2)
			{
				float value = GetSliderValue(vSAInfo[0].vValue[0].iParaType, vSAInfo[0].vValue[0].value / 10);
				if (value != SLIDER_VALUE_ERROR)
				{
					if (vSAInfo[0].vValue[1].iParaType == ParaType_Switch && vSAInfo[0].vValue[1].value == 1)
					{
						addTemperatureBuffEffect(value);
					}
					else {
						addTemperatureBuffDefend(value);
					}
				}
			}

			break;
		}
		case BUFFATTRT_SCREEN_DECALS:
		{
			auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
			if (temperatureComponent)
			{
				if (vSAInfo.size() > 0)
				{
					for (int index = 0; index < (int)vSAInfo.size(); index++)
					{
						if (vSAInfo[index].vValue.size() >= 2) {
							if (vSAInfo[index].vValue[0].iParaType == ParaType_Option_Img && vSAInfo[index].vValue[1].iParaType == ParaType_Switch && vSAInfo[index].vValue[1].value > 0) {
								temperatureComponent->SetScreenDecals(vSAInfo[index].iStatusId, true, vSAInfo[index].vValue[0].value, true);
							}
							else
							{
								temperatureComponent->SetScreenDecals(vSAInfo[index].iStatusId, true, vSAInfo[index].vValue[0].value);
							}
						}
					}
				}
			}
			break;
		}
		case BUFFATTRT_STOP_ACTION:
		{
			auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
			if (temperatureComponent)
			{
				if (vSAInfo.size() > 0)
				{
					temperatureComponent->PauseCurrentFrame(true);
				}
				else
				{
					temperatureComponent->PauseCurrentFrame(false);
				}
			}
			break;
		}
		case BUFFATTRT_CHANGE_MODEL_TEXTURE:
		{

			auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
			if (temperatureComponent) 
			{
				if (vSAInfo.size() > 0)
				{
					for (int index = 0; index < (int)vSAInfo.size(); index++)
					{
						if (vSAInfo[index].vValue.size() >= 3) 
						{
							bool isTransition = false;
							if (vSAInfo[index].vValue[0].value == 1)
							{
								isTransition = true;
							}
							float value = GetSliderValue(vSAInfo[index].vValue[1].iParaType, vSAInfo[index].vValue[1].value);
							float value1 = GetSliderValue(vSAInfo[index].vValue[2].iParaType, vSAInfo[index].vValue[2].value);
							temperatureComponent->ChangeModelTexture(vSAInfo[index].iStatusId, true, isTransition, value, value1);
						}
						else
						{
							bool isTransition = false;
							float value = -3;
							float value1 = -7;
							temperatureComponent->ChangeModelTexture(vSAInfo[index].iStatusId, true, isTransition, value, value1);
						}
					}
				}
			}

			break;
		}
		case BUFFATTRT_HANG_MODEL:
		{
			auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
			if (temperatureComponent)
			{
				if (vSAInfo.size() > 0)
				{
					for (int index = 0; index < (int)vSAInfo.size(); index++)
					{
						if (vSAInfo[index].vValue.size() >= 3)
						{
							int particleId = vSAInfo[index].vValue[0].value;
							float modelscale = vSAInfo[index].vValue[1].value / 10;
							bool isChangeScale = false;
							if (vSAInfo[index].vValue[2].value == 1)
							{
								isChangeScale = true;
							}
							temperatureComponent->Freeze(vSAInfo[index].iStatusId, particleId, modelscale, isChangeScale);
						}
					}
				}
			}
			break;
		}
		case BUFFATTRT_TEMPERATURE_IMPACT_DURATION:
		{
			if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() >= 5)
			{
				auto temperatureComponent = m_OwnerActor->getTemperatureComponent();
				if (temperatureComponent)
				{
					if (vSAInfo.size() > 0)
					{
						for (int index = 0; index < (int)vSAInfo.size(); index++)
						{
							TempImpactDuration tempImpactDuration{};
							tempImpactDuration.BuffId = vSAInfo[index].iStatusId;
							tempImpactDuration.IsLowOrHigh = false;
							if (vSAInfo[index].vValue[0].value == 101501)
							{
								tempImpactDuration.IsLowOrHigh = true;
							}
							tempImpactDuration.TickNum = GetSliderValue(vSAInfo[index].vValue[1].iParaType, vSAInfo[0].vValue[1].value);
							tempImpactDuration.JumpAddTickNum = GetSliderValue(vSAInfo[index].vValue[2].iParaType, vSAInfo[0].vValue[2].value);
							tempImpactDuration.JumpIntervalTickNum = GetSliderValue(vSAInfo[index].vValue[3].iParaType, vSAInfo[0].vValue[3].value);
							tempImpactDuration.PermanentBuffTickNum = GetSliderValue(vSAInfo[index].vValue[4].iParaType, vSAInfo[0].vValue[4].value);
							int iRealStatusId = GetDefManagerProxy()->getRealStatusId(vSAInfo[index].iStatusId, vSAInfo[0].iStatusLv);
							auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
							if (def)
							{
								tempImpactDuration.LimitTime = def->Status.LimitTime;
								temperatureComponent->AddTempImpactDuration(tempImpactDuration);
							}
						}
					}
				}
			}
			break;
		}
		case BUFFATTRT_AROUND_TEMPERATURE_CHANGE:
		{
			if (g_WorldMgr && g_WorldMgr->getTemperatureMgr() && m_OwnerActor)
			{
				auto tmperatureMgr = static_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr());
				if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() > 0)
				{
					if (m_AreaSourceIndex > 0)
						tmperatureMgr->RemoveMobileTemperatureSource(m_AreaSourceIndex);
					if (m_OwnerActor->getWorld())
						m_AreaSourceIndex = tmperatureMgr->AddMobileTemperatureSource(m_OwnerActor->getWorld(), m_OwnerActor->getObjId(), vSAInfo[0].vValue[0].value, -1);
					else
						m_AreaSourceWait = true;
				}
				else
				{
					if (m_AreaSourceIndex > 0)
						tmperatureMgr->RemoveMobileTemperatureSource(m_AreaSourceIndex);
					m_AreaSourceIndex = 0;
					m_AreaSourceWait = false;
				}
			}
			break;
		}
		case BUFFATTRT_LIQUID_BLOCK_CONGEAL:
		{
			if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() > 1)
				m_LiquidBlockEnable = true;
			else
				m_LiquidBlockEnable = false;
			break;
		}
		case BUFFATTRT_AROUND_RANDOM_CREATE_BLOCK:
		{
			if (vSAInfo.size() > 0 && vSAInfo[0].vValue.size() > 3)
				m_CreateBlockEnable = true;
			else
				m_CreateBlockEnable = false;
			break;
		}
		case BUFFATTRT_FORBID_OPERATE:
		{
			if (vSAInfo.size() > 0)
			{
				if (m_OwnerActor == g_pPlayerCtrl)
				{
					auto RidComp = g_pPlayerCtrl->getRiddenComponent();
					if (RidComp && RidComp->isRiding())
					{
						ClientActor* actor = RidComp->getRidingActor();
						if (actor && actor->canDismount())
							g_pPlayerCtrl->tryMountActor(NULL);
					}
				}
			}
			break;
		}
		default:
		{
			break;
		}
	}
}

void LivingAttrib::execNormalEffect(BuffAttrType type)
{
	if (m_OwnerActor == NULL) return;
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo((int)type, vValue);
	execEffect(type, vValue);
}

//特殊效果生效
void LivingAttrib::execSpecialEffect(BuffAttrType type)
{
	if (m_OwnerActor == NULL) return;
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo((int)type, vValue);
	execEffect(type, vValue);
	switch (type)
	{
		case BUFFATTRT_BODY_ALL:
		{
			//全身
			if (vValue.size() > 0)
			{
				//设置整个模型缩放
				float scale = 1.0f;
				for (int i = 0; i < (int)vValue.size(); i++)
				{
					for (int j = 0; j < (int)vValue[i].vValue.size(); j++)
					{
						if (vValue[i].vValue[j].iType != 0)
						{
							scale = scale + ((float)vValue[i].vValue[j].value * 0.01f);
							scale = (scale == 0.0f) ? 1.0f : scale;
						}
					}
				}
				m_OwnerActor->setCustomScale(scale < 0.0f ? 0.0f : scale);
			}
			else
			{
				m_OwnerActor->setCustomScale(1.0f);
			}
			break;
		}
		case BUFFATTRT_BODY_HEAD:
		{
			//头部
			ActorBody *body = m_OwnerActor->getBody();
			if (body)
			{
				if (vValue.size() > 0)
				{
					//设置头部缩放
					float scale = 1.0f;
					for (int i = 0; i < (int)vValue.size(); i++)
					{
						for (int j = 0; j < (int)vValue[i].vValue.size(); j++)
						{
							if (vValue[i].vValue[j].iType != 0) 
							{
								scale = scale + ((float)vValue[i].vValue[j].value * 0.01f);
								scale = (scale == 0.0f) ? 1.0f : scale;
							}
						}
					}
					body->setHeadBoneScale(scale < 0.0f ? 0.0f : scale);
				}
				else
				{
					body->setHeadBoneScale(1.0f);
				}
			}
			break;
		}
		case BUFFATTRT_MODEL_CHANGE:
		{
			//改变模型
			ActorBody *body = m_OwnerActor->getBody();
			if (body)
			{
				if (vValue.size() > 0)
				{
					int objid = -1;
					auto functionWrapper = m_OwnerActor->getFuncWrapper();
					if (functionWrapper)
					{
						functionWrapper->setBodyTypeBeforeMutate(body->getBodyType());
					}

					int szie = (int)vValue.size() - 1;
					if (vValue[szie].vValue.size() > 0 && vValue[szie].vValue[0].value > 0)
					{
						ClientPlayer *player = dynamic_cast<ClientPlayer *>(m_OwnerActor);
						if (player)
						{
							objid = player->getObjId();
							player->changePlayerModel(body->getPlayerIndex(), (int)(vValue[szie].vValue[0].value), NULL);
							//修改模型体积再重新检测生效一下
							this->execSpecialEffect(BUFFATTRT_BODY_ALL);
							this->execSpecialEffect(BUFFATTRT_BODY_HEAD);
							againTakeBuffEffect();
						}
						else
						{
							char modename[64] = {};
							sprintf(modename,"mob_%d", (int)(vValue[szie].vValue[0].value));
							MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->ChangeModel(m_OwnerActor, modename);
						}
					}

					//强制背视角
					if (GetWorldManagerPtr() && vValue[szie].vValue.size() >= 2 && vValue[szie].vValue[1].value)
					{
						if (g_pPlayerCtrl && objid == g_pPlayerCtrl->getObjId() && g_pPlayerCtrl->getViewMode() == CameraControlMode::CAMERA_FPS)  // 不是本机玩家获得改变模型buff不改变视角 
						{
							g_pPlayerCtrl->m_NeedRevertToFPS |= 2;
							g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
							GetIWorldConfigProxy()->setGameData("view", CameraControlMode::CAMERA_TPS_BACK + 1);
						}
					}

				}
				else
				{
					ClientPlayer *player = dynamic_cast<ClientPlayer *>(m_OwnerActor);
					if (player)
					{
						std::string customjson = "";
						auto functionWrapper = m_OwnerActor->getFuncWrapper();
						if (functionWrapper && functionWrapper->getBodyTypeBeforeMutate() == 3)
							customjson = player->getCustomjson();

						std::string custommodel = player->getCustomModel();
						if (custommodel.size() > 0)
						{
							float scale = player->getCustomModelScale();
							player->changeBaseModel(custommodel, scale);
						}
						else
						{
							player->changePlayerModel(body->getPlayerIndex(), 0, customjson.c_str(), NULL,0,0,true);
						}

						if (functionWrapper)
						{
							functionWrapper->setBodyTypeBeforeMutate(player->getBody()->getBodyType());
						}
						//修改模型体积在检测生效一下
						this->execSpecialEffect(BUFFATTRT_BODY_ALL);
						this->execSpecialEffect(BUFFATTRT_BODY_HEAD);
					}
					else
					{
						if (m_OwnerActor->getDefID() > 0)
						{
							char modename[64] = {};
							sprintf(modename, "mob_%d", m_OwnerActor->getDefID());
							MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->ChangeModel(m_OwnerActor, modename);
						}
					}

					if (g_pPlayerCtrl && player == g_pPlayerCtrl && g_pPlayerCtrl->m_NeedRevertToFPS & 2) // 不是本机玩家获得改变模型buff不改变视角 
					{
						g_pPlayerCtrl->m_NeedRevertToFPS &= (~2);
						g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_FPS);
						GetIWorldConfigProxy()->setGameData("view", CameraControlMode::CAMERA_FPS + 1);
					}
						
				}

			}
			break;
		}
		case BUFFATTRT_INVULNERABLE:
		{
			//无敌
			if (vValue.size() > 0)
				m_OwnerActor->setInvulnerable(true);
			else
				m_OwnerActor->setInvulnerable(false);

			break;
		}
		case BUFFATTRT_BUBBLE:
		{
			//泡泡
			ActorBody *body = m_OwnerActor->getBody();
			if (body)
			{
				if (vValue.size() > 0)
					m_OwnerActor->playMotion("BUFF_POP", 1);
				else
					m_OwnerActor->stopMotion("BUFF_POP");
			}

			break;
		}
		case BUFFATTRT_RELEASE_TIRED:
		{
			//add by navy
			auto playerAtt = dynamic_cast<PlayerAttrib*>(this);
			if (!playerAtt) { break; }

			float fValue = getActorAttValueWithStatus((int)type, 0.0f);
			if (fValue > 0.000001f)
			{
				playerAtt->setMaxStrengthForExhaustion(fValue);//设置成自定义
			}
			else
			{
				playerAtt->setMaxStrengthForExhaustion(-1.0f);//还原成配置
			}
			break;
		}
		//2021/07/09 修改：巴拉拉魔棒 codeby:wudeshen
		case BUFFATTRT_MODEL_CHANGE_ROLESKIN:
		{
			ActorBody *body = m_OwnerActor->getBody();
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(m_OwnerActor);
			if (player && body)
			{
				if (vValue.size() > 0)
				{
					if (vValue[0].vValue[0].value > 0)
					{
						int playerNewIndex = ComposePlayerIndex(body->getModelID(), body->getGeniusLv(), vValue[0].vValue[0].value);
						player->changePlayerModel(playerNewIndex, body->getMutateMob(), NULL);
					}
				}
				else
				{
					int playerNewIndex = ComposePlayerIndex(body->getModelID(), body->getGeniusLv(), player->m_originSkinId);
					player->changePlayerModel(playerNewIndex, body->getMutateMob(), player->m_strOriginCustomJson.c_str());
				}
			}
			break;
		}
		case BUFFATTRT_ATTR_SHAPESHIFT:
		{
			// 属性变身，改变多项属性和攻击模式，人物模型
			ActorBody* body = m_OwnerActor->getBody();
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);

			if (body && player)
			{
				if (vValue.size() > 0 && vValue[0].vValue[0].value > 0)
				{
				}
				else
				{
					setAttrShapeShift(false);
				}
			}
			break;
		}
		default:
		{
			break;
		}

	}
}

void LivingAttrib::againTakeBuffEffect()
{
	//特效
	if (m_Buffs.size() > 0 && m_OwnerActor)
	{
		for (int i = m_Buffs.size() - 1; i >= 0; i--)
		{
			ActorBuff backBuff = m_Buffs[i];
			if (backBuff.def && backBuff.def->Status.ParticleID > 0)
			{
				auto def = GetDefManagerProxy()->getParticleDef(backBuff.def->Status.ParticleID);
				if (def) 
				{
					m_OwnerActor->playMotion(def->EffectName.c_str(), 1);
				}
				return;
			}
		}
	}
}

void LivingAttrib::againTakeBuffSound()
{
	//音效
	if (m_Buffs.size() > 0 && m_OwnerActor)
	{
		for (int i = m_Buffs.size() - 1; i >= 0; i--)
		{
			ActorBuff backBuff = m_Buffs[i];
			if (backBuff.def && backBuff.def->Status.SoundID > 0)
			{
				auto def = GetDefManagerProxy()->getSoundDef(backBuff.def->Status.SoundID);
				if (def) {
					if (backBuff.def->SoundType == 1)
					{
						auto sound = m_OwnerActor->getSoundComponent();
						if (sound)
						{
							sound->playSoundFollowActor(def->SoundPath.c_str(), 1.0f, 1.0f, true);
						}
					}
					else
					{
						auto sound = m_OwnerActor->getSoundComponent();
						if (sound)
						{
							sound->playSoundFollowActor(def->SoundPath.c_str(), 1.0f, 1.0f, false);
						}
					}
				}
			}
		}
	}
}

float LivingAttrib::getGeniusValue(PLAYER_GENIUS_TYPE geniustype,int idx)
{
	float geniusvalue[4];
	memset(&geniusvalue, 0, sizeof(geniusvalue));
	ClientPlayer *player = static_cast<ClientPlayer *>(m_OwnerActor);
	if (player)
	{
		float ret0 = player->getGeniusValue(geniustype, geniusvalue);
		if (idx == 0)
		{	//返回值是天赋1
			return ret0; 
		}
		else if (idx < sizeof(geniusvalue)/sizeof(geniusvalue[0]))
		{	//geniusvalue 0-1 对应的天赋2,3
			return geniusvalue[idx-1];
		}
	}
	
	return 0;
}

bool LivingAttrib::getBuffEffectBankInfo(int iAttType)
{
	if (m_OwnerActor->needClear() || isDead() || m_StatusAddAttInfo.size() == 0)
	{
		return false;
	}

	auto it = m_StatusAddAttInfo.find(iAttType);
	if (it != m_StatusAddAttInfo.end())
	{
		return true;
	}

	return false;
}

float LivingAttrib::getWeaponSkinAddOxgen()
{
	float attrPercent = 0.0f;
	ClientPlayer* player = static_cast<ClientPlayer*>(m_OwnerActor);
	if (player != NULL)
	{
		WeaponSkinMgr* weaponSkinMgr = GET_SUB_SYSTEM(WeaponSkinMgr);
		if (weaponSkinMgr != nullptr)
		{
			const char* attrType = "WeaponSkin_System_OnWaterBreathe";
			attrPercent = weaponSkinMgr->GetSkinAdditionPercent(attrType, player);
		}

	}
	return attrPercent;
}

void LivingAttrib::AddImmueBuff(int buffId)
{
    m_immuneBuff.insert(buffId);
}

void LivingAttrib::RemoveImmueBuff(int buffId)
{
    m_immuneBuff.erase(buffId);
}

bool LivingAttrib::CheckImmueBuff(int buffId)
{
	if (m_immuneBuff.count(-1) > 0) {  //全部buff都免疫
		return true;
	}

    if (m_immuneBuff.count(buffId) > 0) {
        return true;
    }

    return false;
}

int LivingAttrib::getWaterPressure()
{ 
	return m_iWaterPressure; 
}

void LivingAttrib::removeBuffEffect(int buffid)
{
	// 通过添加到buff缓冲区的方式直接添加buff，不需要这个接口了
	return;
	if (buffid == 1016 || buffid == 1018)//移除1017001特效
	{
		int iRealStatusId = GetDefManagerProxy()->getRealStatusId(1017, 1);
		auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
		ActorBuff ststatus;
		ststatus.buffid = 1017;
		ststatus.bufflv = 1;
		ststatus.def = def;
		execBuff(&ststatus, 1);
		m_StatusAddAttInfo.clear();
	}
}

//20220708  在沙尘暴天气中减少沙尘暴的伤害，包括骆驼坐骑和坐骑上的玩家 codeby：wangyu
int LivingAttrib::getHurtValueOnLuotuo(int hurtValue)
{
	auto RidComp = m_OwnerActor->getRiddenComponent();
	ClientActor* riding = NULL;
	if (RidComp && RidComp->isRiding())
	{
		riding = RidComp->getRidingActor();
	}
	//玩家骑在骆驼坐骑上
	if ((riding && riding->getDefID() && (riding->getDefID() == HORSE_LUOTUO_LV2)))
	{
		const HorseAbilityDef* abilityDef = GetDefManagerProxy()->getHorseAbilityDef(HORSE_STEP_DUSTSTORM);
		if (abilityDef)
		{
			float ratio = hurtValue * abilityDef->Effect[0];
			hurtValue -= ratio;
		}
	}
	else
	{
		float skillvals[7];
		ActorHorse* horse = dynamic_cast<ActorHorse*>(m_OwnerActor);
		//骆驼坐骑
		if (horse && horse->getHorseSkill(HORSE_STEP_DUSTSTORM, skillvals))
		{
			float ratio = hurtValue * skillvals[0];
			hurtValue -= ratio;
		}
	}

	return hurtValue;
}


void LivingAttrib::temperatureTick()
{
	//if (m_Tick % 20 == 0)
	//{
	//	if (g_WorldMgr)
	//	{
	//		if (!g_WorldMgr->getTemperatureMgr()->GetTemperatureActive())
	//		{
	//			m_Temperature = 0.f;
	//			return;
	//		}
	//		if (!g_WorldMgr->isRemote())
	//		{
	//			float oldFinalTemp = m_FinalPosTemperature;
	//			m_FinalPosTemperature = m_PosTemperature;// 修正后得位置温度
	//			//1. 最终位置温度 = 位置温度 + BUFF效果（能超适宜温度）之和 + 自身抗温属性 + 装备抗温属性之和 + 附魔抗温属性之和 + BUFF效果（不能超适宜温度）之和
	//			//	2. 相加的时候，从左往右的顺序加，每计算一项都做是否超过适宜温度的判断（如果有的话）
	//			m_FinalPosTemperature += m_TemperatureBuffEffect;
	//			m_FinalPosTemperature = clamp(m_FinalPosTemperature, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
	//			checkPosTemperatureSuit(m_FinalPosTemperature, m_TemperatureDefend);
	//			checkPosTemperatureSuit(m_FinalPosTemperature, m_TemperatureBuffDefend);
	//			m_TemperatureEquipDefend = 0;
	//			m_TemperatureEnchantDefend = 0;
	//			for (int i = 0; i < EQUIP_WEAPON; i++)
	//			{
	//				BackPackGrid* grid = getEquipGrid((EQUIP_SLOT_TYPE)i);
	//				if (grid && grid->def)
	//				{
	//					const ToolDef* tool = GetDefManagerProxy()->getToolDef(grid->def->ID);
	//					if (tool)
	//						m_TemperatureEquipDefend += tool->TemperatureDefense;
	//					const GridRuneData& rune = grid->getRuneData();
	//					for (int j = 0; j < rune.getRuneNum(); j++)
	//					{
	//						//const EnchantDef* def = GetDefManagerProxy()->getEnchantDef(grid->getIthEnchant(j));
	//						const RuneDef* def = rune.findDefByIndex(j);
	//						if (def == NULL || def->EnchantType != ENCHANT_TEMPDEF) continue;
	//						m_TemperatureEnchantDefend += rune.getItemByIndex(j).getRuneVal0();
	//					}
	//				}
	//			}
	//			checkPosTemperatureSuit(m_FinalPosTemperature, m_TemperatureEquipDefend);
	//			checkPosTemperatureSuit(m_FinalPosTemperature, m_TemperatureEnchantDefend);

	//			m_FinalPosTemperature = clampadd(m_FinalPosTemperature, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);

	//			if (oldFinalTemp != m_FinalPosTemperature)
	//			{
	//				ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_OwnerActor);
	//				if (player)
	//				{
	//					jsonxx::Object context;
	//					context << "finalPosTemp" << m_FinalPosTemperature;
	//					SandBoxManager::getSingleton().sendToClient(player->getUin(), "PB_TEMPERATURE_HC", context.bin(), context.binLen());
	//				}
	//			}
	//		}
	//	}

	//	if (m_FinalPosTemperature != m_Temperature)
	//	{
	//		float val = m_FinalPosTemperature - m_Temperature;
	//		if (val > 0)
	//		{
	//			float temp = (m_BaseTemperatureChangeVal + m_TickTemperatureChangeVal * abs(m_FinalPosTemperature)) * (m_Temperature > 0 ? 1.0f : m_TickTemperatureChangeRate);
	//			m_Temperature += temp;
	//			if (m_Temperature > m_FinalPosTemperature) m_Temperature = m_FinalPosTemperature;
	//		}
	//		else
	//		{
	//			float temp = (m_BaseTemperatureChangeVal + m_TickTemperatureChangeVal * abs(m_FinalPosTemperature)) * (m_Temperature < 0 ? 1.0f : m_TickTemperatureChangeRate);
	//			m_Temperature -= temp;
	//			if (m_Temperature < m_FinalPosTemperature) m_Temperature = m_FinalPosTemperature;
	//		}
	//	}
	//}
}

void LivingAttrib::radiationTick()
{

}

void LivingAttrib::checkPosTemperatureSuit(float& posTemp, float defend)
{
	posTemp += defend; // 抗温属性
	if (defend > 0.f)
	{
		float heat = GetLuaInterfaceProxy().get_lua_const()->m_ConstTempHeat;
		if (m_PosTemperature <= heat)
		{
			if (posTemp > heat) 
				posTemp = heat;
		}
		else if (m_PosTemperature > heat) 
				posTemp = m_PosTemperature;
	}
	else if (defend < 0.f)
	{
		float ice = GetLuaInterfaceProxy().get_lua_const()->m_ConstTempIce;
		if (ice < m_PosTemperature && posTemp < ice) posTemp = ice;
		if (m_PosTemperature >= ice)
		{
			if (posTemp < ice)
				posTemp = ice;
		}
		else if (m_PosTemperature < ice)
			posTemp = m_PosTemperature;
	}
}

void LivingAttrib::addTemperature(float val, bool suit)
{
	//m_Temperature += val;
	if (suit)
	{
		if (m_Temperature > 0)
		{
			float heat = GetLuaInterfaceProxy().get_lua_const()->m_ConstTempHeat;
			if (m_Temperature + val > heat)
			{
				if(m_Temperature < heat)
					m_Temperature = heat;
			}
			else
			{
				m_Temperature += val;
			}
		}
		else
		{
			float ice = GetLuaInterfaceProxy().get_lua_const()->m_ConstTempIce;
			if (m_Temperature + val < ice)
			{
				if(m_Temperature > ice)
					m_Temperature = ice;
			}
			else
			{
				m_Temperature += val;
			}
		}
	}
	else
	{
		m_Temperature += val;
	}
	m_Temperature = clamp(m_Temperature, TEMPERATURE_MIN_ICE, TEMPERATURE_MAX_HEAT);
}

void LivingAttrib::applyEquipToughness()
{
	auto grid = getEquipGrid(EQUIP_WEAPON);
	if (grid)
	{
		m_ToughnessEquip = grid->getToughness();
		m_ToughnessTotal = m_ToughnessEquip + m_ToughnessBase;
	}
	int itemid = getEquipItem(EQUIP_WEAPON);
	const ToolDef* def = GetDefManagerProxy()->getToolDef(itemid);
	if (def)
	{
		m_ToughnessTotalMax = m_ToughnessBase + def->Toughness;
	}
}

void LivingAttrib::addToughnessTotal(int tou)
{
	m_ToughnessTotal += tou;
	if (m_ToughnessTotal > m_ToughnessTotalMax)
	{
		m_ToughnessTotal = m_ToughnessTotalMax;
	}
	else if (m_ToughnessTotal < 0)
	{
		m_ToughnessTotal = 0;
	}
}

void LivingAttrib::setToughnessBase(int toughness)
{
	m_ToughnessBase = toughness;
}

void LivingAttrib::randomCreateBlockTick()
{
	if (!m_CreateBlockEnable || !m_OwnerActor || !m_OwnerActor->getWorld() || m_OwnerActor->getWorld()->isRemoteMode()) return;
	//if (m_CreateBlockInfo.vValue.size() < 4) return;
	World* pworld = m_OwnerActor->getWorld();
	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_AROUND_RANDOM_CREATE_BLOCK, vValue);
	if (vValue.size() == 0)
	{
		return;
	}
	StatusAttInfo info = vValue[0];
	int time = info.vValue[2].value * 20;
	if (time <= 0) time = 20;
	if (m_Tick % time == 0)
	{
		{
			WCoord blockpos = CoordDivBlock(m_OwnerActor->getPosition());
			int range = info.vValue[0].value;
			int height = info.vValue[1].value + 1;
			int blockid = info.vValue[3].value;

			WCoord curBlockPos = blockpos;
			for (int i = range * range; i > 0; --i)
			{
				int xRand = GenRandomInt(-range, range);
				int zRand = GenRandomInt(-range, range);
				if (Sqrt(xRand * xRand + zRand * zRand) > (float)range) 
					continue;
				for (int y = -height; y <= height; ++y)
				{
					curBlockPos.x = blockpos.x + xRand;
					curBlockPos.y = blockpos.y + y;
					curBlockPos.z = blockpos.z + zRand;

					Block curBlock = pworld->getBlock(curBlockPos);
					if (blockid == BLOCK_SNOWPANE && curBlock.getResID() == BLOCK_SNOWPANE)
					{
						auto data = curBlock.getData();
						if (data < 4)
						{
							pworld->setBlockAll(curBlockPos, blockid, data + 1);
							return;
						}
					}
					else
					{
						if (curBlock.isAir())
						{
							if (blockid == BLOCK_SNOWPANE)
							{
								--curBlockPos.y;
								auto blockdef = GetDefManagerProxy()->getBlockDef(pworld->getBlockID(curBlockPos));
								if (blockdef)
								{
									auto type = blockdef->Type;// 满足该类型得方块上面才能放置雪
									if (type == "basic" || type == "log" || type == "objtree" || type == "scalyfruit" || type == "bluefruit" || type == "giantstone" || type == "grayleaf" || type == "newleaf" || 
										type == "grass" || type == "soil" || type == "solidsand" || type == "reef" || type == "gravel" || type == "ice" || type == "starmushroom")
									{
									}
									else
										return;
								}
								++curBlockPos.y;
							}
							pworld->setBlockAll(curBlockPos, blockid, 0);
							return;
						}
					}
				}
			}
		}
	}
}

inline void isAroundLiquidBlock(int blockid,int& solidid)
{
	switch (blockid)
	{
	case BLOCK_STILL_WATER:
		solidid = BLOCK_ICE;
		break;
	case BLOCK_STILL_LAVA:
		solidid = BLOCK_BLACK_COAGULATION;
		break;
	case BLOCK_STILL_HONEY:
		solidid = BLOCK_HONEY_PRODUCT1;
		break;
	case BLOCK_STILL_SAND:
		solidid = BLOCK_SOLIDSAND;
		break;
	default:
		solidid = 0;
		break;
	}
}


void LivingAttrib::changeAroundLiquidTick()
{
	if (!m_LiquidBlockEnable || !m_OwnerActor || !m_OwnerActor->getWorld() || m_OwnerActor->getWorld()->isRemoteMode()) return;
	if (m_Tick % 2 == 0) return;

	if (m_AreaSourceWait)// 避免进地图没有 world 失效问题
	{
		std::vector<StatusAttInfo> vValue;
		getStatusAddAttInfo(BUFFATTRT_AROUND_TEMPERATURE_CHANGE, vValue);
		if (vValue.size() == 0) return;
		auto tmperatureMgr = static_cast<TemperatureManager*>(g_WorldMgr->getTemperatureMgr());
		m_AreaSourceIndex = tmperatureMgr->AddMobileTemperatureSource(m_OwnerActor->getWorld(), m_OwnerActor->getObjId(), vValue[0].vValue[0].value,  -1);
		m_AreaSourceWait = false;
	}

	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_LIQUID_BLOCK_CONGEAL, vValue);
	if (vValue.size() == 0) return;
	StatusAttInfo info = vValue[0];
	int range = info.vValue[0].value;
	int height = info.vValue[1].value;
	WCoord pos = CoordDivBlock(m_OwnerActor->getPosition());
	WCoord blockpos;
	for (int x = -range; x <= range; x++)
	{
		blockpos.x = pos.x + x;
		for (int z = -range; z <= range; z++)
		{
			blockpos.z = pos.z + z;
			if (Sqrt(x * x + z * z) > range) continue;
			for (int y = height; y > -height; y--)
			{
				blockpos.y = pos.y + y;
				int solidid = 0;
				isAroundLiquidBlock(m_OwnerActor->getWorld()->getBlockID(blockpos), solidid);
				if (solidid != 0)
					m_OwnerActor->getWorld()->setBlockAll(blockpos, solidid, 0);
			}
		}
	}
}

void LivingAttrib::randomAddStateToAttacker(ClientActor* attacker)
{
	if (!m_OwnerActor || !m_OwnerActor->getWorld() || m_OwnerActor->getWorld()->isRemoteMode()) return;

	if (!attacker) return;

	ActorLiving* attackLiving = dynamic_cast<ActorLiving*>(attacker);
	if (!attackLiving) return;

	LivingAttrib* attrib = attackLiving->getLivingAttrib();
	if (!attrib) return;

	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_RANDOM_ATTACKER_GAINSTATE, vValue);
	if (vValue.size() == 0) return;
	StatusAttInfo info = vValue[0];

	int trigAttackNum = info.vValue[0].value;
	int per = info.vValue[1].value;
	int buffid = (info.vValue[2].value) / 1000;

	long long objid = attacker->getObjId();
	if (m_attackedNumMap.find(objid) == m_attackedNumMap.end())
	{
		m_attackedNumMap[objid] = 1;
	}
	else
	{
		m_attackedNumMap[objid]++;
	}
	
	if (m_attackedNumMap[objid] >= trigAttackNum)
	{
		if (GenRandomInt(1, 100) <= per)
		{
			if (attrib->hasBuff(buffid))
			{
				auto buffinfo = attrib->getBuffInfo(attrib->getBuffIndex(buffid));
				if (buffinfo.buffid == buffid)
				{
					if (GetDefManagerProxy()->getBuffDef(buffid, buffinfo.bufflv + 1))
					{
						attrib->addBuff(buffid, buffinfo.bufflv + 1);
					}
				}
			}
			else
			{
				attrib->addBuff(buffid, 1);
			}
		}
	}
}
void LivingAttrib::randomAddStateToAttack(ClientActor* attacker)
{
	if (!m_OwnerActor || !m_OwnerActor->getWorld() || m_OwnerActor->getWorld()->isRemoteMode()) return;

	if (!attacker) return;


	std::vector<StatusAttInfo> vValue;
	getStatusAddAttInfo(BUFFATTRT_RANDOM_BEATTACK_GAINSTATE, vValue);
	if (vValue.size() == 0) return;
	StatusAttInfo info = vValue[0];

	int trigAttackNum = info.vValue[0].value;
	int per = info.vValue[1].value;
	int buffid = (info.vValue[2].value) / 1000;

	long long objid = attacker->getObjId();
	if (m_attackedNumMap.find(objid) == m_attackedNumMap.end())
	{
		m_attackedNumMap[objid] = 1;
	}
	else
	{
		m_attackedNumMap[objid]++;
	}

	if (m_attackedNumMap[objid] >= trigAttackNum)
	{
		if (GenRandomInt(1, 100) <= per)
		{
			if (hasBuff(buffid))
			{
				auto buffinfo =getBuffInfo(getBuffIndex(buffid));
				if (buffinfo.buffid == buffid)
				{
					if (GetDefManagerProxy()->getBuffDef(buffid, buffinfo.bufflv + 1))
					{
						addBuff(buffid, buffinfo.bufflv + 1);
					}
				}
			}
			else
			{
				addBuff(buffid, 1);
			}
		}
	}
}

bool LivingAttrib::removeBuffFix2(ActorBuff* buff)
{
	int buffid = buff->buffid;
	int instanceId = buff->buffidx;
	execBuff(buff, 1);
	if (m_delegateBuffRemove)
	{
		m_delegateBuffRemove(buffid, buff->bufflv);
	}
	//m_Buffs.erase(m_Buffs.begin() + idx); 和 removeBuffByIndex 不同的地方
	//重新生效buf特效、音效
	//againTakeBuffEffect();  //原来的逻辑有bug
	//againTakeBuffSound();   
	static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);

	//removeBuffEffect(buffid);
	return true;
}

bool LivingAttrib::removeBuffFix(int buffid, bool bRemoveSingleOne /*= true*/, int buffInstanceId /*= 0*/)
{
	bool ret = false;
#if 0
	bool isNew = isNewStatus();
	if (isNew)
	{
		bool isCustomStatus = GetDefManagerProxy()->isCustomStatus(buffid);
		for (size_t i = 0; i < m_Buffs.size(); )
		{
			if (isCustomStatus)
			{
				if (m_Buffs[i].buffid == buffid && m_Buffs[i].def)
				{
					if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId)
					{
						int instanceId = (m_Buffs[i].def->BuffType == 2 ? 0 : m_Buffs[i].buffidx);
						execBuff(&m_Buffs[i], 1);
						if (m_delegateBuffRemove)
						{
							m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
						}
						//m_Buffs.erase(m_Buffs.begin() + i); //不同的地方

						static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);
						if (bRemoveSingleOne)//删除单个，默认删除vector的第一个
						{
							ret = true;
							break;
						}
					}
					else
					{
						i++;
					}
				}
				else
				{
					i++;
				}
			}
			else
			{//老的状态不会同时存在多个
				if (m_Buffs[i].buffid == buffid)
				{
					if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId)
					{
						int instanceId = m_Buffs[i].buffidx;
						execBuff(&m_Buffs[i], 1);
						if (m_delegateBuffRemove)
						{
							m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
						}
						//m_Buffs.erase(m_Buffs.begin() + i); //不同的地方

						static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);
						ret = true;
						break;
					}
					else
					{
						i++;
					}
				}
				else
				{
					i++;
				}
			}
		}
	}
	else
	{
		bool find = false;
		size_t i = 0;
		for (; i < m_Buffs.size(); i++)
		{
			if (m_Buffs[i].buffid == buffid)
			{
				find = true;
				break;
			}
		}
		if (find)
		{
			//if (i == m_Buffs.size()) return;

			execBuff(&m_Buffs[i], 1);

			//if (i + 1 < m_Buffs.size()) m_Buffs[i] = m_Buffs.back();
			if (m_delegateBuffRemove)
			{
				m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
			}
			//m_Buffs.resize(m_Buffs.size() - 1);

			if (buffid == INVULNERABLE_BUFF)
			{
				m_OwnerActor->setInvulnerable(false);
			}

			static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0);
			ret = true;
		}
		
	}
#else
	bool isCustomStatus = GetDefManagerProxy()->isCustomStatus(buffid);
	for (size_t i = 0; i < m_Buffs.size(); )
	{
		if (isCustomStatus)
		{
			if (m_Buffs[i].buffid == buffid && m_Buffs[i].def)
			{
				if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId)
				{
					int instanceId = (m_Buffs[i].def->BuffType == 2 ? 0 : m_Buffs[i].buffidx);
					execBuff(&m_Buffs[i], 1);
					if (m_delegateBuffRemove)
					{
						m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
					}
					//m_Buffs.erase(m_Buffs.begin() + i); //不同的地方

					static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);
					if (bRemoveSingleOne)//删除单个，默认删除vector的第一个
					{
						ret = true;
						break;
					}
				}
				else
				{
					i++;
				}
			}
			else
			{
				i++;
			}
		}
		else
		{//老的状态不会同时存在多个
			if (m_Buffs[i].buffid == buffid)
			{
				if (buffInstanceId <= 0 || m_Buffs[i].buffidx == buffInstanceId)
				{
					int instanceId = m_Buffs[i].buffidx;
					execBuff(&m_Buffs[i], 1);
					if (m_delegateBuffRemove)
					{
						m_delegateBuffRemove(buffid, m_Buffs[i].bufflv);
					}
					//m_Buffs.erase(m_Buffs.begin() + i); //不同的地方

					static_cast<ActorLiving*>(m_OwnerActor)->onBuffChange(1, buffid, 0, 0, instanceId);
					ret = true;
					break;
				}
				else
				{
					i++;
				}
			}
			else
			{
				i++;
			}
		}
	}
#endif

	removeBuffEffect(buffid);
	return ret;
}

void LivingAttrib::addArrowById(int itemId, int count/* = 1*/)
{
	if (m_arrows.find(itemId) == m_arrows.end())
	{
		m_arrows[itemId] = count;
	}
	else
	{
		int lastCount = m_arrows[itemId];
		m_arrows[itemId] = lastCount + count;
	}
}

void LivingAttrib::returnArrows()
{
	if (m_OwnerActor == NULL || m_arrows.empty()) return;

	// 仅开放冒险模式
	if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isAdventureMode())
		return;
	
	auto ActionAttrStateComp = m_OwnerActor->getActionAttrStateComponent();
	if (ActionAttrStateComp && ActionAttrStateComp->checkActionAttrState(ENABLE_DEATHDROPITEM))
	{
		auto dropComponent = m_OwnerActor->GetComponent<DropItemComponent>();
		if (dropComponent == NULL) return;

		auto iter = m_arrows.begin();
		while (iter != m_arrows.end())
		{
			int itemId = iter->first;
			int count = round(iter->second * 0.7);
			if (count > 0) dropComponent->dropItem(itemId, count);

			iter++;
		}
		m_arrows.clear();
	}
}


EQUIP_BACK_INDEX LivingAttrib::getEquipIdxByType(EQUIP_SLOT_TYPE type)
{
	if (type >= EQUIP_WEAPON)
	{
		return EQUIP_BACK_INDEX_NONE;
	}
	auto it = m_EquipType2Pos.find(type);	
	if (it == m_EquipType2Pos.end())
	{
		return EQUIP_BACK_INDEX_NONE;
	}
	return (EQUIP_BACK_INDEX)it->second.getSourceTypeORIdx();
}


#include "FurnaceContainerArray.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "PlayerControl.h"
#include "IPlayerControl.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "ClientActorManager.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialMgr.h"

#include "ObserverEvent.h" 
#include "ObserverEventManager.h"
//#include "GameEvent.h"
//#include "TaskSubSystem.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "SandboxCoreDriver.h"
#include "world.h"
#include "Platforms/PlatformInterface.h"
static int ox = 0;
static int oy = 30;
static int oz = 0;

EXPORT_SANDBOXENGINE extern int CheckInsertItemIntoArray(BaseContainer* container, BackPackGrid* gridarray, int arraylen, const BackPackGrid& grid);

void FurnaceContainerArray::Initialization()
{
	//for (int i = 0; i < GRID_FULL; i++)
	for (int i = 0; i < m_totalGird; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	auto itemdef = GetDefManagerProxy()->getItemDef(632);
	m_Grids[m_SwitchOnIdx].setItemDef(itemdef);
	m_Grids[m_SwitchOnIdx].setNum(2);
	m_Grids[m_SwitchOnIdx + 1].setItemDef(itemdef);
	m_Grids[m_SwitchOnIdx + 1].setNum(1);

	m_isMelting = false;
	m_temperature = 1;
	isLoad = false;
	for (auto& item : m_CurHeatArray)
	{
		item = 0;
	}
	for (auto& item : m_MaxHeatArray)
	{
		item = 1;
	}
	for (auto& item : m_ProvideHeatArray)
	{
		item = 0.0f;
	}
	for (auto& item : m_MeltTicksArray)
	{
		item = 0;
	}

	for (auto& item : m_MeltTicksFloatArray)
	{
		item = 0;
	}

	for (auto& item : m_BurnOnceTimeArray)
	{
		item = 0;
	}

	m_huoyanPos.clear();
	for (int i = 0; i < 4; i++)
	{
		m_scene_halo_id[i] = 0;
	}

	m_SwitchOn = false;
}

FurnaceContainerArray::FurnaceContainerArray() : WorldContainer(FURNACE_START_INDEX), m_bilu_wood_ent_id(0)
{
	m_blockid = 0;
	m_FurnaceType = 0;
	m_FuelCount = 0;
	m_MatCount = 0;
	m_ResultCount = 0;
	Initialization();
}

FurnaceContainerArray::FurnaceContainerArray(const WCoord &blockpos, int blockid) : WorldContainer(blockpos, FURNACE_START_INDEX), m_bilu_wood_ent_id(0)
{
	m_FurnaceType = 0;
	m_FuelCount = 0;
	m_MatCount = 0;
	m_ResultCount = 0;

	Initialization();
}

FurnaceContainerArray::~FurnaceContainerArray()
{
	
}

void FurnaceContainerArray::UpdateFurnaceType()
{
	if (m_World)
	{
		m_blockid = m_World->getBlockID(m_BlockPos);

		auto blockdef = GetDefManagerProxy()->getBlockDef(m_blockid);
		if (blockdef)
		{
			m_FurnaceType = blockdef->EditType;
		}
	}
	MINIW::ScriptVM::game()->callFunction("GetFurnaceDef", "i>iii", m_FurnaceType, &m_MatCount, &m_FuelCount, &m_ResultCount);
}


void FurnaceContainerArray::meltOnce(int type)
{
	assert(m_Grids[type].getNum() > 0);
	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(m_Grids[type].getItemID(), false, m_FurnaceType);
	if (def == NULL) return;

	GetDefManagerProxy()->checkCrcCode(CRCCODE_FURNACE);

	if (g_pPlayerCtrl)
	{
		//g_pPlayerCtrl->addAchievement(1, ACHIEVEMENT_FURNACEITEM, def->Result, 1);
		g_pPlayerCtrl->addAchievement(1, ACHIEVEMENT_FURNACEITEM, getFurnaceResultId(def), getFurnaceResultNum(def)); //code_by:huangfubin 不同温度的产物不一样
		g_pPlayerCtrl->addOWScore(def->Score);
	}
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_FURNACE_ITEM, m_BlockPos * BLOCK_SIZE, getFurnaceResultId(def), 0, getFurnaceResultNum(def));
	}*/
	WCoord pos = m_BlockPos * BLOCK_SIZE;
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", TASKSYS_FURNACE_ITEM).
			SetData_Userdata("WCoord", "trackPos", &pos).
			SetData_Number("target1", getFurnaceResultId(def)).
			SetData_Number("target2", 0).
			SetData_Number("goalnum", getFurnaceResultNum(def));
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
	}
	int index = getCanUseResultGrid(type);
	if (index != -1)
	{
		SetBackPackGrid(m_Grids[index], getFurnaceResultId(def), m_Grids[index].getNum() + getFurnaceResultNum(def));
		afterChangeGrid(m_BaseIndex + index);
	}

	SetBackPackGridWithClear(m_Grids[type], def->MaterialID, m_Grids[type].getNum() - 1);

	/*if (m_Grids[GRID_MTL].getNum() <= 0 && m_Grids[GRID_MTL2].getNum() <= 0 && m_Grids[GRID_MTL3].getNum() <= 0)
		m_isMelting = false;*/

	//if (type == GRID_MTL)
	//{
	//	m_MeltTicks = 0;
	//	m_MeltTicksFloat = 0.0;
	//}
	//else if (type == GRID_MTL2)
	//{
	//	m_MeltTicks2 = 0;
	//	m_MeltTicksFloat2 = 0.0;
	//}
	//else if (type == GRID_MTL3)
	//{
	//	m_MeltTicks3 = 0;
	//	m_MeltTicksFloat3 = 0.0;
	//}

	m_MeltTicksArray[type] = 0;
	m_MeltTicksFloatArray[type] = 0.0;

	afterChangeGrid(m_BaseIndex + type);

	ObserverEvent obevent;
	obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
	obevent.SetData_Furance(def->ID);
	ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.end", &obevent);
}

void FurnaceContainerArray::addHeatOnce()
{  
	if (!m_SwitchOn)
		return;

	m_isMelting = true;
	bool bAllConditionFalse = true;
	const FurnaceDef** fueldefs = new const FurnaceDef*[m_FuelCount];
	for (int i = 0; i < m_FuelCount; i++)
	{
		auto& fuelgrid = m_Grids[m_fuelStartIdx + i];
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(fuelgrid.getItemID(), false, m_FurnaceType);
		fueldefs[i] = def;
		if (def)
		{
			if (fuelgrid.getNum() >= 0 && def != NULL && def->Heat > 0)
			{
				bAllConditionFalse = false;
			}
		}
	}

	if (isHeatEmtpy() && bAllConditionFalse)
	{
		m_isMelting = false;
		setSwitchOn(false);
		return;
	}
	
	for (int i = 0; i < m_FuelCount; i++)
	{
		const FurnaceDef* curDef = fueldefs[i];
		if (m_CurHeatArray[i] == 0)
		{
			m_ProvideHeatArray[i] = 0.0;
		}

		if (curDef && m_CurHeatArray[i] == 0)
		{
			int grid_idx = m_fuelStartIdx + i;
			// 有些燃料也有产物 先检测能否产出
			if (curDef->Result > 0)
			{
				int retgrid_idx = getCanUseResultGrid(grid_idx);
				if (retgrid_idx == -1)
				{
					continue;
				}
				else
				{
					auto& resultgrid = m_Grids[retgrid_idx];
					if (resultgrid.getItemID() == curDef->Result)
					{
						resultgrid.setNum(resultgrid.getNum() + curDef->ResultNum);
					}
					else
					{
						SetBackPackGridWithClear(resultgrid, curDef->Result, curDef->ResultNum);
					}
					afterChangeGrid(m_BaseIndex + retgrid_idx);
				}
			}

			m_CurHeatArray[i] = curDef->Heat;
			m_MaxHeatArray[i] = curDef->Heat;
			m_ProvideHeatArray[i] = curDef->ProvideHeat;
			
			auto& fuelgrid = m_Grids[grid_idx];
			SetBackPackGridWithClear(fuelgrid, fuelgrid.getItemID(), fuelgrid.getNum() - 1);

			afterChangeGrid(m_BaseIndex + grid_idx);
		}
	}
	delete fueldefs;

	if (isHeatEmtpy() && isMtlEmtpy())
	{
		m_isMelting = false;
		setSwitchOn(false);
		notifyChange2Openers(-1, true);
	}
}

void FurnaceContainerArray::setSwitchOn(bool on)
{
	m_SwitchOn = on;
	m_Grids[m_SwitchOnIdx].setNum(on ? 2 : 1);
	m_Grids[m_SwitchOnIdx + 1].setNum(on ? 1 : 2);
}

bool FurnaceContainerArray::isMtlEmtpy()
{
	for (int i = 0; i < m_MatCount; i++)
	{
		if (m_Grids[m_mtlStartIdx + i].getNum() > 0)
			return false;
	}
	return true;
}

bool FurnaceContainerArray::isHeatEmtpy()
{
	for (int i = 0; i < m_FuelCount; i++)
	{
		if (m_CurHeatArray[i] != 0)
			return false;
	}
	return true;
}



BackPackGrid *FurnaceContainerArray::index2Grid(int index)
{
	assert(index>=m_BaseIndex && index<m_BaseIndex + m_totalGird);

	return &m_Grids[index-m_BaseIndex];
}

void FurnaceContainerArray::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);

	int useidex = index - FURNACE_START_INDEX;

	for (int i = 0; i < m_MatCount; i++)
	{
		if (useidex == m_mtlStartIdx + i)
		{
			afterChangeGridByMTL(m_mtlStartIdx + i);
		}
	}

	for (int i = 0; i < m_FuelCount; i++)
	{
		if (useidex == m_fuelStartIdx + i)
		{
			notifyChange2Openers(-1, true);
			break;
		}
	}

	if (useidex == m_SwitchOnIdx)
	{
		m_SwitchOn = m_Grids[m_SwitchOnIdx].getNum() > 1;
		m_NeedSave = true;
		notifyChange2Openers(-1, true);
	}
	m_NeedSave = true;
}

void FurnaceContainerArray::afterChangeGridByMTL(int type)
{
	if (m_Grids[type].getNum() == 0)
	{
		m_MeltTicksArray[type] = 0;
		m_MeltTicksFloatArray[type]= 0.0;
		notifyChange2Openers(-1, true);		
	}
}

void FurnaceContainerArray::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < m_totalGird; i++)
	{
		// GameEventQue::GetInstance().postBackpackChange(FURNACE_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", FURNACE_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	// GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void FurnaceContainerArray::onDetachUI()
{
	m_AttachToUI = false;
}

bool FurnaceContainerArray::canPutItem(int index)
{
	if (index >= (m_BaseIndex + m_resultStartIdx)  &&
		index < (m_BaseIndex + m_resultStartIdx + m_ResultCount) )
	{
		return false;
	}
	else
	{
		return true;
	}

}

void FurnaceContainerArray::onHeatOnOff()
{
	if (m_vehicleWorld != nullptr) { return; }
	int blockdata = m_World->getBlockData(m_BlockPos);
	int blockid = m_World->getBlockID(m_BlockPos);
	int newblockdata = blockdata;
	bool bAllCurHeatZero = true;
	for (const auto& item : m_CurHeatArray)
	{
		if (item != 0)
		{
			bAllCurHeatZero = false;
			break;
		}
	}
	// if ((m_CurHeat | m_CurHeat2 | m_CurHeat3) == 0 && !m_isMelting){
	if (bAllCurHeatZero && !m_isMelting) {
		newblockdata = blockdata & 3;
		m_World->setBlockData(m_BlockPos, newblockdata, 2);
		m_World->setBlockTemperature(m_BlockPos, 0);
	}
	else {
		newblockdata = blockdata | 4;
		m_World->setBlockData(m_BlockPos, newblockdata, 2);
		m_World->setBlockTemperature(m_BlockPos, BlockMaterial::getTemperatureValue(blockid));
		
	}
	if (newblockdata != blockdata)
	{
		UpdateEffect();
	}
}

void FurnaceContainerArray::dropItems()
{
	for (int i = 0; i < m_totalGird - 2; i++)
	{
		if (m_Grids[i].getItemID() > 0)
			dropOneItem(m_Grids[i]);
	}
}

void FurnaceContainerArray::updateTickMTL(int type, float allProvideHeat, bool &hasprogress)
{
	if (m_isMelting && getCanUseResultGrid(type) != -1)
	{
		
			if (m_MeltTicksFloatArray[type] == 0.0)
			{
				const FurnaceDef* furnacedef = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(m_Grids[type].getItemID(), false, m_FurnaceType);
				if (furnacedef)
				{
					ObserverEvent obevent;
					obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
					obevent.SetData_Furance(furnacedef->ID);
					ObserverEventManager::getSingleton().OnTriggerEvent("Furnace.begin", &obevent);
				}
			}

			if (m_BurnOnceTimeArray[type] / allProvideHeat < 20)//原料熔炼至少tick次数
			{
				allProvideHeat = m_BurnOnceTimeArray[type] / 20;
			}
			m_MeltTicksFloatArray[type] += allProvideHeat;
			m_MeltTicksArray[type] = m_MeltTicksFloatArray[type];
			if (m_MeltTicksFloatArray[type] >= m_BurnOnceTimeArray[type])
			{
				meltOnce(type);
			}
		
		hasprogress = true;
	}
}
void FurnaceContainerArray::UpdateEffect()
{
	int blockid = m_World->getBlockID(m_BlockPos, true);
	BlockMaterial* blockMtrl = blockid > 0 ? g_BlockMtlMgr.getMaterial(blockid) : nullptr;
	if (blockMtrl)
	{
		blockMtrl->DoOnPlayRandEffect(m_World, m_BlockPos);//onPlayRandEffect(m_World, blockpos);
	}
}
void FurnaceContainerArray::updateTick()
{
	if (m_NeedComputOfflineRsult)
	{
		m_NeedComputOfflineRsult = false;
		ComputOfflineRsult();
	}

	if(m_World->isRemoteMode()) return;

	if (!m_SwitchOn)
	{
		return;
	}

	bool hasprogress = false;
	bool oldmelting = m_isMelting;

	int oldmeltticksArray[m_Max] = {0};
	int oldcurheat = 0;
	int oldmaxheat = 0;
	float lodprovideheat = 0;
	float allProvideHeat = 0;
	for (int i = 0; i < m_FuelCount; i++)
	{
		oldcurheat += m_CurHeatArray[i];
		oldmaxheat += m_MaxHeatArray[i];
		lodprovideheat += m_ProvideHeatArray[i];
	}
	for (int i = 0; i < m_MatCount; i++)
	{
		oldmeltticksArray[i] = m_MeltTicksArray[i];
	}
	allProvideHeat = lodprovideheat;
	m_allProvideHeat = allProvideHeat;


	for (int i = 0; i < m_MatCount; i++)
	{
		updateTickMTL(m_mtlStartIdx + i, allProvideHeat, hasprogress);
	}

	bool needCheckHeat = false;
	for (int i = 0; i < m_FuelCount; i++)
	{
		if (m_CurHeatArray[i] > 0)
		{
			m_CurHeatArray[i]--;
			needCheckHeat = true;
			hasprogress = true;
		}
	}

	onHeatOnOff();

	if(m_SwitchOn)
	{
		for (int i = 0; i < m_FuelCount; ++i)
		{
			if (m_CurHeatArray[i] == 0)
			{
				addHeatOnce();
				break;
			}
		}
	}

	if(hasprogress)
	{
		notifyChange2Openers(-1, true);
		//GameEventQue::GetInstance().postBackPackAttribChange();
	}

	bool bmelttickChang = false;
	for (int i = 0; i < m_MatCount; i++)
	{
		if (oldmeltticksArray[i] != m_MeltTicksArray[i])
		{
			bmelttickChang = true;
			break;
		}
	}
	int totoalHeat = 0;
	int totoalMaxHeat = 0;
	int totoalprovideHeat = 0;
	for (int i = 0; i < m_FuelCount; i++)
	{
		totoalHeat += m_CurHeatArray[i];
		totoalMaxHeat += m_MaxHeatArray[i];
		totoalprovideHeat += m_ProvideHeatArray[i];
	}

	if (oldmelting != m_isMelting ||
		bmelttickChang ||
		oldcurheat != totoalHeat ||
		oldmaxheat != totoalMaxHeat ||
		lodprovideheat != totoalprovideHeat)
	{
		m_NeedSave = true;
	}
}

float FurnaceContainerArray::getHeatPercent(int index)
{
	if (index >= m_FuelCount)
	{
		return 0.0f;
	}
	if (m_MaxHeatArray[index] == 0) m_MaxHeatArray[index] = 1;
	return float(m_CurHeatArray[index]) / float(m_MaxHeatArray[index]);
}

float FurnaceContainerArray::getMeltTicksPercent(int index)
{
	if (index >= m_MatCount)
	{
		return 0.0f;
	}
	if (m_BurnOnceTimeArray[index] == 0.00)
	{
		return 0.0f;
	}
	return float(m_MeltTicksFloatArray[index] / m_BurnOnceTimeArray[index]);
}

void FurnaceContainerArray::clear()
{
	for (int i = 0; i < m_totalGird; i++) {
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int FurnaceContainerArray::addItemByCount(int itemid, int num)
{
	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(itemid, false, m_FurnaceType);
	if (def == NULL) return -1;

	int index = m_BaseIndex;
	BackPackGrid *dest;
	if (def->Heat > 0) {
		dest = &m_Grids[m_mtlStartIdx];
		index += m_mtlStartIdx;
	}
	else {
		dest = &m_Grids[m_fuelStartIdx];
		index += m_fuelStartIdx;
	}

	if (!dest->isEmpty() && dest->getItemID() != itemid) { return -1; }

	int addnum = num;
	if (dest->isEmpty()) {
		SetBackPackGrid(*dest, itemid, num);
		afterChangeGrid(index);
	}
	else {
		addnum = dest->addNum(num);
	}

	return addnum;
}

void FurnaceContainerArray::removeItemByCount(int itemid, int num)
{
	for (int i = 0; i < m_totalGird; i++)
	{
		BackPackGrid *grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> FurnaceContainerArray::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> grids[m_totalGird];
	for (int i = 0; i < m_totalGird; i++)
	{
		grids[i] = m_Grids[i].save(builder);
	}

	auto items = builder.CreateVector(grids, m_totalGird);
	auto curheats = builder.CreateVector(m_CurHeatArray, m_FuelCount);
	auto maxheats = builder.CreateVector(m_MaxHeatArray, m_FuelCount);
	auto provideheats = builder.CreateVector(m_ProvideHeatArray, m_FuelCount);
	auto meltticks = builder.CreateVector(m_MeltTicksArray, m_MatCount);
	int m_MeltTicks = 0;// m_MeltTicksFloat;
	int m_MeltTicks2 = 0;// m_MeltTicksFloat2;
	int m_MeltTicks3 = 0;// m_MeltTicksFloat3;
	m_OfflineTime = MINIW::GetTimeStamp();
	auto actor = FBSave::CreateContainerFurnaceArray(builder, basedata, items, 
		m_isMelting,
		m_temperature,
		curheats, maxheats, provideheats, meltticks,
		m_OfflineTime
		);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFurnaceArray, actor.Union());
}

EXPORT_SANDBOXENGINE extern int g_BackgridCheckNumMethod;

bool FurnaceContainerArray::load(const void *srcdata)
{
	isLoad = true;
	auto src = reinterpret_cast<const FBSave::ContainerFurnaceArray *>(srcdata);
	loadContainerCommon(src->basedata());

	m_isMelting = src->melting()!=0;
	m_temperature = src->temperature();

	// 初始化所有数组元素为0
	for (int i = 0; i < m_Max; i++)
	{
		m_CurHeatArray[i] = 0;
		m_MaxHeatArray[i] = 1;
		m_ProvideHeatArray[i] = 0.0f;
		m_MeltTicksArray[i] = 0;
		m_MeltTicksFloatArray[i] = 0.0f;
		m_BurnOnceTimeArray[i] = 0;
	}

	// 从文件加载数据
	for (unsigned int i = 0; i < src->curheatArray()->size(); i++)
	{
		if (i < m_FuelCount)
		{
			m_CurHeatArray[i] = src->curheatArray()->Get(i);
		}
	}
	for (unsigned int i = 0; i < src->maxheatArray()->size(); i++)
	{
		if (i < m_FuelCount)
		{
			m_MaxHeatArray[i] = src->maxheatArray()->Get(i);
		}
	}
	for (unsigned int i = 0; i < src->provideHeatArray()->size(); i++)
	{
		if (i < m_FuelCount)
		{
			m_ProvideHeatArray[i] = src->provideHeatArray()->Get(i);
		}
	}
	for (unsigned int i = 0; i < src->meltticksArray()->size(); i++)
	{
		if (i < m_MatCount)
		{
			m_MeltTicksArray[i] = src->meltticksArray()->Get(i);
		}
	}

	g_BackgridCheckNumMethod = 1;
	for (unsigned int i = 0; i < src->items()->size(); i++)
	{
		if (i >= m_totalGird)
		{
			break;
		}
		m_Grids[i].load(src->items()->Get(i));
	}
	m_SwitchOn = m_Grids[m_SwitchOnIdx].getNum() > 1;
	m_OfflineTime = src->offlineTime();
	g_BackgridCheckNumMethod = 0;

	int offtimeTick = MINIW::GetTimeStamp() - m_OfflineTime;
	if (offtimeTick > 0)
		m_NeedComputOfflineRsult = true;
	return true;
}

// virtual float getAttrib(int i) 参考主机的
int FurnaceContainerArray::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < m_totalGird; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}
	
	for (int i = 0; i < 27; i++)
	{
		pAttrInfos->Add(getAttrib(i));
	}

	return m_totalGird;
}

int FurnaceContainerArray::onInsertItem(const BackPackGrid &grid, int num, int params)
{
	return -1;
}

// 传送带相关的
bool FurnaceContainerArray::canInsertItem(const BackPackGrid& grid, int param)
{
	return false;
}

BackPackGrid *FurnaceContainerArray::onExtractItem(int params)
{
	return NULL;
}

int FurnaceContainerArray::calComparatorInputOverride()
{
	return CalculateItemsComparatorInput(&m_Grids[0], 3);
}


int FurnaceContainerArray::getCanUseResultGrid(int type)
{
	if (type < 0 || type >= m_totalGird)
	{
		assert(false && "type out of range");
		return -1;
	}
	auto& grid = m_Grids[type];
	if (grid.getNum() <= 0) 
		return -1;

	const FurnaceDef *def = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(grid.getItemID(), false, m_FurnaceType);
	if (!def)
		return -1;

	int result_id = def->Result; //对应不同熔炉产物itemid
	int result_num = getFurnaceResultNum(def);

	const ItemDef* resultdef = GetDefManagerProxy()->getItemDef(result_id);
	if (resultdef == NULL) return -1;

	int retidx = -1;
	// 优先放入相同道具格子
	for (int j = 0; j < m_ResultCount; j++)
	{
		int idx = m_resultStartIdx + j;
		auto& retgrid = m_Grids[idx];
		if (retgrid.getItemID() == result_id && (retgrid.getNum() + result_num) <= resultdef->StackMax)
		{
			retidx = idx;
			break;
		}
	}

	if (retidx == -1)
	{
		// 检查空格子
		for (int j = 0; j < m_ResultCount; j++)
		{
			int idx = m_resultStartIdx + j;
			auto& retgrid = m_Grids[idx];

			if (retgrid.isEmpty())
			{
				retidx = idx;
				break;
			}
		}
	}

	if (retidx != -1 && type >= m_mtlStartIdx && type < m_mtlStartIdx + m_MatCount)
		m_BurnOnceTimeArray[type] = def->BurnTime;

	return retidx;
}

int FurnaceContainerArray::getFurnaceResultId(const FurnaceDef *def)
{
	if (def == NULL) return 0;
	int result_id = def->Result; //对应不同熔炉产物itemid
	return result_id;
}

void FurnaceContainerArray::setTemperatureLev(int lev)
{

}

int FurnaceContainerArray::getFurnaceResultNum(const FurnaceDef *def)
{
	//旧版本原来没有resultnum，统一默认1
	if (def == NULL) return 1;
	int num = def->ResultNum; //对应不同熔炉产物itemid
	return num;
}


void FurnaceContainerArray::leaveWorld() 
{	
	for (int i = 0; i < 4; i++)
	{		
		if (m_scene_halo_id[i] && m_World->getEffectMgr()->getEffectByID(m_scene_halo_id[i]))
		{
			m_World->getEffectMgr()->stopParticleEffectByID(m_scene_halo_id[i]);
		}
	}

	if (m_bilu_wood_ent_id && m_World->getEffectMgr()->getEffectByID(m_bilu_wood_ent_id))
	{
		m_World->getEffectMgr()->stopParticleEffectByID(m_bilu_wood_ent_id);
	}

	WorldContainer::leaveWorld();
}

//有燃料 或热量 返回true
bool FurnaceContainerArray::canSwitchOn()
{
	for (int i =0; i < m_FuelCount; i++)
	{
		if (m_CurHeatArray[i] > 0)
		{
			return true;
			break;
		}
	}

	for (int i = 0; i < m_FuelCount; i++)
	{
		if (m_Grids[m_fuelStartIdx + i].getNum() > 0)
		{
			return true;
			break;
		}
	}

	return false;
	
}

void FurnaceContainerArray::ComputOfflineRsult()
{
	if (m_World->isRemoteMode()) return;
	bool hasFuel = false;//是否有燃料
	for (int i = 0; i < m_FuelCount; i++)
	{
		if (m_Grids[m_fuelStartIdx + i].getNum() > 0)
		{
			hasFuel = true;//有燃料
			break;
		}
	}
	if (!hasFuel)//没有燃料
		return;


	bool hasMtl = false;//是否有原料
	for (int i = 0; i < m_MatCount; i++)
	{
		if (m_Grids[m_mtlStartIdx + i].getNum() > 0)
		{
			hasMtl = true;//有原料
			break;
		}
	}
	if (!hasMtl)//没有原料
		return;

	int offtimeTick = MINIW::GetTimeStamp() - m_OfflineTime;
	if (offtimeTick > 30)
	{
		offtimeTick = 30;
	}
	//todo 计算离线时间内的熔炼结果 这里性能差，需要优化
	for (int i = 0; i < offtimeTick; i++)
	{
		updateTick();
	}
}

float FurnaceContainerArray::getAttrib(int i)
{
	if (i >= m_mtlStartIdx && i < m_mtlStartIdx + m_Max)
	{
		return getMeltTicksPercent(i); //原料
	} 
	else if (i >= m_fuelStartIdx && i < m_fuelStartIdx + m_Max)
	{
		return getHeatPercent(i - m_fuelStartIdx);  //燃料  
	}
	else if (i == 20)   // 是否正在熔炼
		return m_SwitchOn ? 1 : 0;
	else if (i == 21)   // 一个tick提供的热量
		return m_allProvideHeat;
	else if (i== 22)    // 是否可以启动
		return canSwitchOn() ? 1 : 0;
	else if (i == 23)   // 熔炉类型
		return m_FurnaceType;
	else if (i == 24)   // 原料数量
		return m_MatCount;
	else if (i == 25)   // 燃料数量
		return m_FuelCount;
	else if (i == 26)   // 熔炉产物数量
		return m_ResultCount;
	return 0;
}
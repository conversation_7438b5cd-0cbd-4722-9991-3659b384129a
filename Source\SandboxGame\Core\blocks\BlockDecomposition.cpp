#include "BlockDecomposition.h"
#include "BlockMaterialMgr.h"
#include "IClientPlayer.h"
#include "world.h"
#include "IClientGameInterface.h"
#include "Collision.h"
#include "VehicleWorld.h"
#include "IClientGameManagerInterface.h"
#include "backpack.h"

#include "SoundComponent.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"
#include "ClientPlayer.h"

#include "container_decomposition.h"

IMPLEMENT_BLOCKMATERIAL(BlockDecompositionMaterial)
void BlockDecompositionMaterial::init(int resid)
{
	MultiModelBlockMaterial::init(resid);
    SetToggle(BlockToggle_HasContainer, true);
}

void BlockDecompositionMaterial::initMultiRange(const BlockDef* def)
{
	m_MultiRange.setElement(1, 1, 0);
}

bool BlockDecompositionMaterial::onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	if (container && player)
	{
		// 是否禁止多人操作
		if (container->isOpenning())
		{
			return false;
		}

		pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), "misc.chest_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
		player->openContainer(container);
		return true;
	}

	return false;
}

WorldContainer* BlockDecompositionMaterial::createMultiContainer(World* pworld, const WCoord& blockpos)
{
	ContainerDecomposition* container = SANDBOX_NEW(ContainerDecomposition, blockpos);
	return container;
}

int BlockDecompositionMaterial::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		// 对于板块，我们可以使用现有的朝向或者一个固定值
		return playerTmp->getCurPlaceDir();
	}
	return 0;
}

void BlockDecompositionMaterial::update(unsigned int dtick)
{
	Super::update(dtick);

	//LOG_INFO("BlockDecompositionMaterial::update %d", dtick);
}

void BlockDecompositionMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return ;
	int blockdata = playerTmp->getCurPlaceDir();
	pworld->setBlockData(blockpos, blockdata, 3);

	int nGuideTask = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_getCurNoviceGuideTask",nGuideTask);
	if(playerTmp->getOWID() == NEWBIEWORLDID && nGuideTask == 13)
	{
		auto pgame = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
		if(pgame)
		{
			int x = blockpos.x * 100 + 50;
			int y = blockpos.y * 100 + 50;
			int z = blockpos.z * 100 - 20;
			pgame->playEffect(x, y, z, "1034.ent");
		}
	}
}

int BlockDecompositionMaterial::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int dir = sectionData->getBlock(blockpos).getData() & 3;

	idbuf[0] = 0;
	dirbuf[0] = dir;
	return 1;
}

void BlockDecompositionMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord p0 = blockpos*BLOCK_SIZE;
	coldetect->addObstacle(p0, p0+WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

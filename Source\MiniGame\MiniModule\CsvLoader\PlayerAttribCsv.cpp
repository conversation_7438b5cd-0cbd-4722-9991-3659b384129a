#include "PlayerAttribCsv.h" 
#include "OgreUtils.h"
#include "OgreStringUtil.h"
#include "defmanager.h"
#include "ModManager.h"
#include "OgreStringUtil.h"
#include "ModManager.h"
using MINIW::CSVParser;
using namespace MINIW;
IMPLEMENT_LAZY_SINGLETON(PlayerAttribCsv)
PlayerAttribCsv::PlayerAttribCsv()
{
}
PlayerAttribCsv::~PlayerAttribCsv()
{
	onClear();
}

using ExternalAttrChange = PlayerAttribCsvDef::ExternalAttrChange;
using InternalAttrChange = PlayerAttribCsvDef::InternalAttrChange;
using BuffConfig = PlayerAttribCsvDef::BuffConfig;

std::vector<BuffConfig> parseBuffConfig(const std::string& input) {
	std::vector<BuffConfig> result;
	std::string trimmed = input.substr(1, input.size() - 2); // Remove the outer braces '{ }'
	std::stringstream ss(trimmed);
	std::string item;

	while (std::getline(ss, item, ']')) { // Read until ']'
		if (item.empty()) continue;

		// Find the beginning of the data '[', and extract the relevant substring
		size_t start = item.find('[');
		if (start != std::string::npos) {
			item = item.substr(start + 1); // Get rid of '['

			std::stringstream dataStream(item);
			
			char delimiter;
			BuffConfig data;
			data.id = 0;
			data.level = 0;
			data.threshold = 0;

			dataStream >> data.id >> delimiter // Parse int (key) and skip comma
				>> data.level >> delimiter // Parse level and skip comma
				>> data.threshold; // Parse threshold

			result.push_back(data);
		}
	}
	return result;
}

std::map<int, ExternalAttrChange> parseExternalAttrChange(const std::string& input) {
	std::map<int, ExternalAttrChange> result;
	std::string trimmed = input.substr(1, input.size() - 2); // Remove the outer braces '{ }'
	std::stringstream ss(trimmed);
	std::string item;

	while (std::getline(ss, item, ']')) { // Read until ']'
		if (item.empty()) continue;

		// Find the beginning of the data '[', and extract the relevant substring
		size_t start = item.find('[');
		if (start != std::string::npos) {
			item = item.substr(start + 1); // Get rid of '['

			std::stringstream dataStream(item);
			
			char delimiter;
			ExternalAttrChange data;
			data.type = 0;
			data.timesec = 0;
			data.value = 0;

			dataStream >> data.type >> delimiter // Parse int (key) and skip comma
				>> data.timesec >> delimiter // Parse float and skip comma
				>> data.value; // Parse float

			result[data.type] = data; // Insert into map
		}
	}
	return result;
}

std::map<int, InternalAttrChange> parseInternalAttrChange(const std::string& input) {
    std::map<int, InternalAttrChange> result;

    // 如果字符串为空或者长度小于 5，如 "" 或 "[]"
    if (input.empty() || input.size() < 5) return result;

    // 去除前后的方括号
    size_t start = input.find('[');
    size_t end = input.find(']');
    if (start == std::string::npos || end == std::string::npos || end <= start + 1) return result;

    std::string content = input.substr(start + 1, end - start - 1); // e.g. "1,0.0035"
    std::stringstream ss(content);

    InternalAttrChange data;
    char delimiter;

    ss >> data.type >> delimiter >> data.value;

    if (!ss.fail()) {
        result[data.type] = data;
    }

    return result;
}

void PlayerAttribCsv::onParse(CSVParser& parser)
{
	m_Table.clear();
	

	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if (id == 0) continue;

		PlayerAttribCsvDef def;
		//std::memset(&def, 0, sizeof(def));
		def.ID = id;
		def.Name = ColumnLang(parser[i], "Name");
		def.InitVal = parser[i]["InitVal"].Float();
		def.MaxVal = parser[i]["MaxVal"].Float();
		def.MinVal = parser[i]["MinVal"].Float();
		def.ReviveVal = parser[i]["ReviveVal"].Float();
		def.Recovery = parser[i]["Recovery"].Float();
		
		std::string str = parser[i]["ExternalDecreace"].Str();
		if (!str.empty())
		{
			def.ExternalDecreace = parseExternalAttrChange(str);
		}

		str = parser[i]["ExternalIncreace"].Str();
		if (!str.empty())
		{
			def.ExternalIncreace = parseExternalAttrChange(str);
		}

		str = parser[i]["InternalDecreace"].Str();
		if (!str.empty())
		{
			def.InternalDecreace = parseInternalAttrChange(str);
		}

		str = parser[i]["Buffs"].Str();
		if (!str.empty())
		{
			def.Buffs = parseBuffConfig(str);
			std::sort(def.Buffs.begin(), def.Buffs.end(), [](const BuffConfig &a, const BuffConfig &b) {
				return a.threshold > b.threshold; // 按 threshold 降序排序
			});			
		}

		str = parser[i]["Debuffs"].Str();
		if (!str.empty())
		{
			def.Debuffs = parseBuffConfig(str);
			std::sort(def.Debuffs.begin(), def.Debuffs.end(), [](const BuffConfig &a, const BuffConfig &b) {
				return a.threshold < b.threshold; // 按 threshold 升序排序
			});			
		}
		
		m_Table.AddRecord(def.ID, def);
	}
}

void PlayerAttribCsv::onClear()
{
}

const char* PlayerAttribCsv::getName()
{
	return "PlayerAttribDef_new";
}

const char* PlayerAttribCsv::getClassName()
{
	return "PlayerAttribCsv";
}

int PlayerAttribCsv::getNum()
{
	load();
	return m_Table.GetRecordSize();
}

const PlayerAttribCsvDef* PlayerAttribCsv::get(PlayerAttributeType type)
{
	load();
	int id = static_cast<int>(type);
	return m_Table.GetRecord(id);
}

std::string PlayerAttribCsv::getPlayerAttribCsvDebuffs(int type)
{
	const PlayerAttribCsvDef* def = get(PlayerAttributeType(type));
	if (!def) return "[]";

	jsonxx::Array arr_json;

	for (const auto& it : def->Debuffs)
	{
		jsonxx::Object item_obj;

		item_obj << "id" << it.id;
		item_obj << "level" << it.level;
		item_obj << "threshold" << it.threshold;

		arr_json.import(item_obj);
	}

	return arr_json.json();
}
